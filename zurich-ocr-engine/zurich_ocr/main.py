"""
Zurich OCR Engine - Main FastAPI Application
Advanced OCR solution with LLM-powered intelligent routing
"""

import asyncio
import io
import logging
import time
import uuid
import json
from typing import Dict, Any, Optional, List, Union
import traceback
from datetime import datetime

from fastapi import FastAPI, File, UploadFile, HTTPException, Form, BackgroundTasks, Depends
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

# Local imports - components
from .core.config import settings
from .core.intelligent_router import intelligent_router, DocumentAnalysis, ProcessingStrategy
from .core.google_document_ai_hub import google_ai_hub, ProcessingResult
from .core.universal_format_engine import universal_format_engine, FileFormatInfo, ExtractionResult
from .core.gpt_postprocessor import gpt_postprocessor
from .core.gpt_postprocessor_v2 import gpt_postprocessor_v2
from .core.mixed_content_analyzer import mixed_content_analyzer
from .core.preprocessing_engine import preprocessing_engine
from .core.ocr_engine_router import ocr_engine_router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Helper functions for document processing
def _convert_ocr_to_google_result(ocr_result):
    """Convert OCR result to Google AI result format for compatibility"""
    from zurich_ocr.core.google_document_ai_hub import ProcessingResult

    if ocr_result.success:
        return ProcessingResult(
            success=True,
            text=ocr_result.text,
            confidence=ocr_result.confidence,
            processor_used=ocr_result.processor_used,
            processing_time=ocr_result.processing_time,
            page_count=1,
            entities=[],
            tables=[],
            form_fields=[]
        )
    else:
        return ProcessingResult(
            success=False,
            text="",
            confidence=0.0,
            processor_used=ocr_result.processor_used,
            processing_time=ocr_result.processing_time,
            page_count=0,
            entities=[],
            tables=[],
            form_fields=[],
            error_message=ocr_result.error_message
        )

async def _tesseract_fallback(file_data: bytes, filename: str):
    """Tesseract OCR fallback for when other methods fail"""
    try:
        import pytesseract
        from PIL import Image
        import io

        image = Image.open(io.BytesIO(file_data))
        tesseract_text = pytesseract.image_to_string(image, config='--psm 6')

        if tesseract_text.strip():
            from zurich_ocr.core.google_document_ai_hub import ProcessingResult
            result = ProcessingResult(
                success=True,
                text=tesseract_text.strip(),
                confidence=0.8,  # Reasonable confidence for Tesseract
                processor_used="tesseract_fallback",
                processing_time=0.5,
                page_count=1,
                entities=[],
                tables=[],
                form_fields=[]
            )
            logger.info(f"Tesseract OCR fallback successful for {filename}")
            return result
        else:
            raise Exception("Tesseract returned empty text")

    except Exception as e:
        logger.warning(f"Tesseract OCR fallback failed for {filename}: {str(e)}")
        from zurich_ocr.core.google_document_ai_hub import ProcessingResult
        return ProcessingResult(
            success=False,
            text="",
            confidence=0.0,
            processor_used="tesseract_fallback",
            processing_time=0.0,
            page_count=0,
            entities=[],
            tables=[],
            form_fields=[],
            error_message=f"Tesseract fallback failed: {str(e)}"
        )

# FastAPI application
app = FastAPI(
    title="Zurich OCR Engine",
    description="""
    Advanced OCR processing with LLM-powered intelligent routing.

    Features:
    - GPT-4 powered intelligent document routing
    - Google Document AI integration with 40+ specialized processors
    - Universal file format support (30+ formats)
    - Advanced OCR challenge resolution
    - Insurance industry optimization
    - High-performance parallel processing
    - Comprehensive quality assessment
    """,
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for API requests and responses
class ProcessingConfig(BaseModel):
    """Configuration for document processing"""
    llm_routing_enabled: bool = Field(default=True, description="Enable LLM-powered intelligent routing")
    parallel_processing: bool = Field(default=True, description="Enable parallel processing for complex documents")
    confidence_threshold: float = Field(default=0.85, description="Minimum confidence threshold for results")
    enable_fallbacks: bool = Field(default=True, description="Enable fallback processing strategies")
    debug_mode: bool = Field(default=False, description="Enable detailed debug information")
    insurance_optimization: bool = Field(default=True, description="Enable insurance industry optimizations")

    # OCR Engine Selection (when LLM routing disabled)
    ocr_engine: Optional[str] = Field(default=None, description="OCR engine: 'gemini', 'gemini1.5', 'gemini2.5pro', 'gemini2.5flash', 'google', or 'tesseract'")
    google_processor: Optional[str] = Field(default="OCR_PROCESSOR", description="Google processor type")
    tesseract_psm: Optional[int] = Field(default=6, description="Tesseract PSM mode")

    # Post-processing Configuration
    post_processing: Union[str, bool] = Field(default="v1", description="Post-processing: 'v1', 'v2', or False")

    # Preprocessing Configuration
    preprocessing: Union[str, bool] = Field(default="auto", description="Preprocessing: True, False, 'auto', 'minimal', 'basic', 'enhanced', 'aggressive'")
    preprocessing_force_level: Optional[str] = Field(default=None, description="Override AI preprocessing decision")
    preprocessing_techniques: Optional[Dict[str, Union[bool, str]]] = Field(default=None, description="Fine-grained preprocessing control")

class ProcessingResponse(BaseModel):
    """Comprehensive processing response"""
    success: bool
    extracted_text: str
    confidence: float
    document_type: str
    processor_used: str
    processing_time_ms: float

    # Structured data
    entities: List[Dict[str, Any]] = []
    tables: List[Dict[str, Any]] = []
    form_fields: List[Dict[str, Any]] = []

    # Metadata
    metadata: Dict[str, Any]
    format_info: Dict[str, Any]

    # Analysis results
    document_analysis: Optional[Dict[str, Any]] = None
    processing_strategy: Optional[Dict[str, Any]] = None

    # GPT Post-processing results
    postprocessed_data: Optional[Dict[str, Any]] = None
    content_regions: Optional[List[Dict[str, Any]]] = None
    drawings_metadata: Optional[List[Dict[str, Any]]] = None

    # Step-by-step timing information
    step_timings: Optional[Dict[str, float]] = None
    gpt_substep_timings: Optional[Dict[str, float]] = None

    # Debug information
    debug_info: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None

class HealthResponse(BaseModel):
    """Health check response"""
    status: str
    timestamp: str
    version: str
    components: Dict[str, Any]

class ConfigResponse(BaseModel):
    """Configuration response"""
    supported_formats: List[str]
    processors: Dict[str, Any]
    features: Dict[str, Any]
    settings: Dict[str, Any]

# API Endpoints

@app.get("/api/v1/health", response_model=HealthResponse)
async def health_check():
    """
    Comprehensive health check endpoint

    Returns detailed status of all system components including:
    - LLM routing system
    - Google Document AI integration
    - Universal format engine
    - Processing statistics
    """

    try:
        # Check component health
        components = {
            "llm_router": {
                "status": "healthy" if intelligent_router.llm_client else "disabled",
                "statistics": intelligent_router.get_processing_statistics()
            },
            "google_ai_hub": {
                "status": "healthy" if google_ai_hub.client else "disabled",
                "processor_status": google_ai_hub.get_processor_status()
            },
            "universal_format_engine": {
                "status": "healthy",
                "supported_formats": len(universal_format_engine.supported_formats)
            },
            "settings": {
                "llm_routing_enabled": settings.LLM_ROUTING_ENABLED,
                "parallel_processing": settings.PARALLEL_PROCESSING,
                "debug_mode": settings.DEBUG_MODE
            }
        }

        return HealthResponse(
            status="healthy",
            timestamp=datetime.now().isoformat(),
            version="2.0.0",
            components=components
        )

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return HealthResponse(
            status="unhealthy",
            timestamp=datetime.now().isoformat(),
            version="2.0.0",
            components={"error": str(e)}
        )

@app.get("/readiness", status_code=200)
async def readiness_check():
    """
    Readiness check endpoint for AWS ALB/API Gateway.

    This endpoint is used by AWS Application Load Balancer to determine
    if the service is ready to receive traffic. It performs a lightweight
    check to ensure the service is operational.
    """
    try:
        # Perform basic readiness checks
        checks = {
            "service": "zurich-ocr-engine",
            "status": "ready",
            "timestamp": datetime.now().isoformat(),
            "version": "2.0.0"
        }

        # Optional: Add basic component checks
        if hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY:
            checks["llm_configured"] = True
        else:
            checks["llm_configured"] = False

        return JSONResponse(content=checks)

    except Exception as e:
        logger.error(f"Readiness check failed: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={
                "service": "zurich-ocr-engine",
                "status": "not_ready",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@app.post("/api/v1/extract-text", response_model=ProcessingResponse)
async def extract_text(
    file: UploadFile = File(...),
    config: Optional[str] = Form(None)
):
    """
    Text extraction endpoint with LLM-powered intelligent routing

    This endpoint provides advanced OCR processing with:
    - GPT-4 powered document analysis and routing
    - Google Document AI integration with 40+ specialized processors
    - Universal file format support (30+ formats)
    - Advanced OCR challenge resolution
    - Insurance industry optimization
    - High-performance parallel processing

    Args:
        file: Document file to process
        config: Optional JSON configuration for processing parameters

    Returns:
        ProcessingResponse: Comprehensive processing result with extracted text,
        structured data, confidence scores, and detailed metadata
    """

    start_time = time.time()
    request_id = str(uuid.uuid4())
    filename = file.filename or f"document_{request_id[:8]}"

    # Initialize step timing tracker
    step_timings = {}
    step_start_time = start_time

    logger.info(f"Starting processing for {filename} (ID: {request_id})")

    try:
        # Parse configuration
        processing_config = ProcessingConfig()
        if config:
            try:
                config_dict = json.loads(config)
                processing_config = ProcessingConfig(**config_dict)
            except Exception as e:
                logger.warning(f"Invalid config provided, using defaults: {str(e)}")

        # Read file data
        file_data = await file.read()

        # Validate file size
        if len(file_data) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE} bytes"
            )

        # Step 1: Universal format detection and extraction
        logger.info(f"Detecting format for {filename}")
        step_start_time = time.time()
        format_info = await universal_format_engine.detect_format(file_data, filename)
        extraction_result = await universal_format_engine.extract_content(file_data, filename)
        step_timings["1_format_detection_and_extraction"] = (time.time() - step_start_time) * 1000

        # Step 1.5: Initialize preprocessing variables (actual preprocessing moved after LLM analysis)
        preprocessed_file_data = file_data
        preprocessing_result = None
        step_timings["1.5_preprocessing"] = 0.0  # Will be updated later

        # Step 2: Document type classification and routing decision
        document_analysis = None
        processing_strategy = None

        # Classify document types for specialized handling
        office_formats = {"xlsx", "docx", "pptx"}  # Office documents - use Google Document AI digital parsing
        text_formats = {"txt", "csv", "html", "rtf"}  # Text documents - use native digital parsers
        ocr_formats = {"png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp", "pdf"}  # OCR required formats

        document_category = None
        if format_info.extension.lower() in office_formats:
            document_category = "office"
        elif format_info.extension.lower() in text_formats:
            document_category = "text"
        elif format_info.extension.lower() in ocr_formats or format_info.format_type == "image":
            document_category = "ocr"
        else:
            document_category = "other"

        # Determine processing path based on document category and configuration
        if document_category in ["office", "text"]:
            # Office and text documents: Skip LLM routing, use specialized processing
            if processing_config.llm_routing_enabled:
                logger.info(f"Overriding LLM routing for {document_category} document: {format_info.extension}")

            step_timings["2_llm_routing_analysis"] = 0.0  # Skipped
            step_timings["2.5_preprocessing_update"] = 0.0  # Skipped
            document_analysis = None
            processing_strategy = None

        elif document_category == "ocr" and processing_config.llm_routing_enabled:
            # OCR documents with LLM routing enabled
            logger.info(f"Performing LLM analysis for {filename}")
            step_start_time = time.time()
            document_analysis = await intelligent_router.analyze_document(
                file_data, filename, {"format_info": format_info.__dict__}
            )
            processing_strategy = await intelligent_router.create_processing_strategy(document_analysis)
            step_timings["2_llm_routing_analysis"] = (time.time() - step_start_time) * 1000

            # Step 2.5: Preprocessing after LLM analysis (if LLM recommends it)
            if (processing_config.preprocessing and
                processing_config.preprocessing != False and
                processing_config.preprocessing != "none" and
                document_analysis and
                hasattr(document_analysis, 'preprocessing_strategy') and
                document_analysis.preprocessing_strategy):

                logger.info("Performing preprocessing based on LLM analysis")
                step_start_time = time.time()

                preprocessing_result = await preprocessing_engine.preprocess_image(
                    file_data,
                    level=processing_config.preprocessing,
                    format_info=format_info,
                    document_analysis=document_analysis,
                    force_techniques=processing_config.preprocessing_techniques.get("techniques") if processing_config.preprocessing_techniques else None
                )

                if preprocessing_result.success:
                    preprocessed_file_data = preprocessing_result.processed_image_data
                    logger.info(f"Preprocessing completed: {len(preprocessing_result.techniques_applied)} techniques, {preprocessing_result.quality_improvement_estimate:.1%} improvement")
                else:
                    logger.warning(f"Preprocessing failed: {preprocessing_result.error_message}")

                step_timings["2.5_preprocessing_update"] = (time.time() - step_start_time) * 1000
            else:
                step_timings["2.5_preprocessing_update"] = 0.0

        elif document_category == "ocr" and not processing_config.llm_routing_enabled and processing_config.ocr_engine:
            # OCR documents with direct engine routing
            logger.info(f"Direct OCR routing: {processing_config.ocr_engine}")
            step_timings["2_llm_routing_analysis"] = 0.0  # Skipped

            # Step 2.5: Preprocessing for direct OCR (if configured)
            if (processing_config.preprocessing and
                processing_config.preprocessing != False and
                processing_config.preprocessing != "none"):

                logger.info(f"Performing preprocessing for direct OCR: {processing_config.preprocessing}")
                step_start_time = time.time()

                preprocessing_result = await preprocessing_engine.preprocess_image(
                    file_data,
                    level=processing_config.preprocessing,
                    format_info=format_info,
                    document_analysis=None,
                    force_techniques=processing_config.preprocessing_techniques.get("techniques") if processing_config.preprocessing_techniques else None
                )

                if preprocessing_result.success:
                    preprocessed_file_data = preprocessing_result.processed_image_data
                    logger.info(f"Preprocessing completed: {len(preprocessing_result.techniques_applied)} techniques, {preprocessing_result.quality_improvement_estimate:.1%} improvement")
                else:
                    logger.warning(f"Preprocessing failed: {preprocessing_result.error_message}")

                step_timings["2.5_preprocessing_update"] = (time.time() - step_start_time) * 1000
            else:
                step_timings["2.5_preprocessing_update"] = 0.0

            document_analysis = None
            processing_strategy = None
        else:
            # Other cases
            step_timings["2_llm_routing_analysis"] = 0.0  # Skipped
            step_timings["2.5_preprocessing_update"] = 0.0  # Skipped

        # Step 3: Document Processing based on category
        google_ai_result = None
        step_start_time = time.time()

        if document_category == "office":
            # Office documents: Google Document AI digital + layout parser → Gemini → Tesseract
            logger.info(f"Processing office document with Google Document AI digital parsing: {filename}")

            try:
                # Try Google Document AI digital parsing first
                from .core.intelligent_router import ProcessingStrategy
                digital_strategy = ProcessingStrategy(
                    primary_processor="LAYOUT_PARSER_PROCESSOR",  # Best for office documents
                    enhancement_processors=[],
                    preprocessing_steps=[],
                    confidence_threshold=0.8,
                    fallback_strategy=[],
                    parallel_processing=False,
                    batch_processing=False
                )

                google_ai_result = await google_ai_hub.process_document(
                    file_data,  # Use original file data for office documents
                    digital_strategy,
                    filename
                )

                if google_ai_result.success:
                    logger.info(f"Google Document AI digital parsing successful for {filename}")
                else:
                    raise Exception(f"Google Document AI failed: {google_ai_result.error_message}")

            except Exception as e:
                logger.warning(f"Google Document AI digital parsing failed for {filename}: {str(e)}")

                # Fallback to Gemini Vision if configured
                if processing_config.ocr_engine and processing_config.ocr_engine.startswith("gemini"):
                    logger.info(f"Trying Gemini Vision fallback for office document: {filename}")
                    try:
                        ocr_result = await ocr_engine_router.process_with_engine(
                            file_data,  # Use original file data
                            filename,
                            processing_config.ocr_engine,
                            processor=processing_config.google_processor,
                            psm_mode=processing_config.tesseract_psm
                        )

                        if ocr_result.success:
                            google_ai_result = _convert_ocr_to_google_result(ocr_result)
                            logger.info(f"Gemini Vision fallback successful for {filename}")
                        else:
                            raise Exception(f"Gemini Vision failed: {ocr_result.error_message}")

                    except Exception as gemini_e:
                        logger.warning(f"Gemini Vision fallback failed for {filename}: {str(gemini_e)}")
                        google_ai_result = None

                # Final fallback to Tesseract
                if not google_ai_result or not google_ai_result.success:
                    logger.info(f"Trying Tesseract fallback for office document: {filename}")
                    google_ai_result = await _tesseract_fallback(file_data, filename)

        elif document_category == "text":
            # Text documents: Native digital parsers → Gemini → Tesseract
            logger.info(f"Processing text document with native digital parsing: {filename}")

            # Try direct extraction first (native digital parsing)
            if extraction_result.success and extraction_result.text.strip():
                from zurich_ocr.core.google_document_ai_hub import ProcessingResult
                google_ai_result = ProcessingResult(
                    success=True,
                    text=extraction_result.text,
                    confidence=0.95,
                    processor_used="native_digital_parser",
                    processing_time=0.1,
                    page_count=1,
                    entities=[],
                    tables=[],
                    form_fields=[]
                )
                logger.info(f"Native digital parsing successful for {filename}")
            else:
                # Fallback to Gemini Vision if configured
                if processing_config.ocr_engine and processing_config.ocr_engine.startswith("gemini"):
                    logger.info(f"Trying Gemini Vision fallback for text document: {filename}")
                    try:
                        ocr_result = await ocr_engine_router.process_with_engine(
                            file_data,  # Use original file data
                            filename,
                            processing_config.ocr_engine,
                            processor=processing_config.google_processor,
                            psm_mode=processing_config.tesseract_psm
                        )

                        if ocr_result.success:
                            google_ai_result = _convert_ocr_to_google_result(ocr_result)
                            logger.info(f"Gemini Vision fallback successful for {filename}")
                        else:
                            raise Exception(f"Gemini Vision failed: {ocr_result.error_message}")

                    except Exception as gemini_e:
                        logger.warning(f"Gemini Vision fallback failed for {filename}: {str(gemini_e)}")
                        google_ai_result = None

                # Final fallback to Tesseract
                if not google_ai_result or not google_ai_result.success:
                    logger.info(f"Trying Tesseract fallback for text document: {filename}")
                    google_ai_result = await _tesseract_fallback(file_data, filename)

        elif document_category == "ocr":
            # OCR documents: Follow LLM routing or direct OCR routing
            if processing_strategy and (extraction_result.requires_ocr or format_info.format_type == "image"):
                # LLM-routed processing
                logger.info(f"Processing with Google Document AI using {processing_strategy.primary_processor}")
                google_ai_result = await google_ai_hub.process_document(
                    preprocessed_file_data, processing_strategy, filename
                )

            elif not processing_config.llm_routing_enabled and processing_config.ocr_engine and (extraction_result.requires_ocr or format_info.format_type == "image"):
                # Direct engine routing
                logger.info(f"Direct OCR processing with {processing_config.ocr_engine}")

                ocr_result = await ocr_engine_router.process_with_engine(
                    preprocessed_file_data,
                    filename,
                    processing_config.ocr_engine,
                    processor=processing_config.google_processor,
                    psm_mode=processing_config.tesseract_psm
                )

                # Convert OCR result to Google AI result format for compatibility
                google_ai_result = _convert_ocr_to_google_result(ocr_result)

        else:
            # Other document types - try direct extraction
            if extraction_result.success and extraction_result.text.strip():
                from zurich_ocr.core.google_document_ai_hub import ProcessingResult
                google_ai_result = ProcessingResult(
                    success=True,
                    text=extraction_result.text,
                    confidence=0.95,
                    processor_used="direct_extraction",
                    processing_time=0.1,
                    page_count=1,
                    entities=[],
                    tables=[],
                    form_fields=[]
                )

        step_timings["3_ocr_processing"] = (time.time() - step_start_time) * 1000

        # Error handling: If all processing methods failed, return error
        if not google_ai_result or not google_ai_result.success:
            error_message = f"All processing methods failed for {filename}"
            logger.error(error_message)

            from zurich_ocr.core.google_document_ai_hub import ProcessingResult
            google_ai_result = ProcessingResult(
                success=False,
                text="",
                confidence=0.0,
                processor_used="error",
                processing_time=0.0,
                page_count=0,
                entities=[],
                tables=[],
                form_fields=[],
                error_message=error_message
            )

        # Step 4: GPT Post-Processing (if enabled)
        postprocessed_result = None

        if (processing_config.post_processing and
            processing_config.post_processing != False and
            processing_config.post_processing != "none" and
            google_ai_result and google_ai_result.success):

            logger.info(f"Starting GPT post-processing V{processing_config.post_processing} for {filename}")
            step_start_time = time.time()

            # Prepare Google Document AI output for post-processing
            raw_ocr_output = {
                'text': google_ai_result.text,
                'entities': google_ai_result.entities,
                'tables': google_ai_result.tables,
                'form_fields': google_ai_result.form_fields,
                'confidence': google_ai_result.confidence,
                'processor_used': google_ai_result.processor_used,
                'page_count': google_ai_result.page_count
            }

            # Determine document type for specialized processing
            doc_type = "insurance"  # Default
            if document_analysis:
                doc_type = document_analysis.document_type

            # Choose post-processing version
            if processing_config.post_processing == "v2":
                # V2 Speed-optimized post-processing
                postprocessed_result_v2 = await gpt_postprocessor_v2.process_document(
                    raw_ocr_output, doc_type
                )

                # Convert V2 result to V1 format for compatibility
                if postprocessed_result_v2.success:
                    from zurich_ocr.core.gpt_postprocessor import ProcessedContent, ContentRegion
                    postprocessed_result = ProcessedContent(
                        structured_data=postprocessed_result_v2.structured_data,
                        content_regions=[],  # V2 doesn't generate content regions
                        drawings_metadata=[],  # V2 doesn't generate drawings metadata
                        processing_time=postprocessed_result_v2.processing_time,
                        confidence=postprocessed_result_v2.confidence,
                        success=True,
                        substep_timings=postprocessed_result_v2.substep_timings
                    )
                else:
                    from zurich_ocr.core.gpt_postprocessor import ProcessedContent
                    postprocessed_result = ProcessedContent(
                        structured_data={},
                        content_regions=[],
                        drawings_metadata=[],
                        processing_time=postprocessed_result_v2.processing_time,
                        confidence=0.0,
                        success=False,
                        error_message=postprocessed_result_v2.error_message,
                        substep_timings=postprocessed_result_v2.substep_timings
                    )
            else:
                # V1 Full-featured post-processing
                postprocessed_result = await gpt_postprocessor.process_document(
                    raw_ocr_output, doc_type
                )

            step_timings["4_gpt_postprocessing"] = (time.time() - step_start_time) * 1000
            logger.info(f"GPT post-processing V{processing_config.post_processing} completed in {postprocessed_result.processing_time:.1f}s")
        else:
            step_timings["4_gpt_postprocessing"] = 0.0  # Skipped

        # Step 5: Combine results
        final_text = ""
        confidence = 0.0
        processor_used = "direct_extraction"
        entities = []
        tables = []
        form_fields = []

        # Post-processing results
        postprocessed_data = None
        content_regions = None
        drawings_metadata = None
        gpt_substep_timings = None

        if postprocessed_result and postprocessed_result.success:
            # Use GPT post-processed results (highest quality)
            postprocessed_data = postprocessed_result.structured_data
            content_regions = [region.__dict__ for region in postprocessed_result.content_regions] if postprocessed_result.content_regions else None
            drawings_metadata = postprocessed_result.drawings_metadata
            gpt_substep_timings = postprocessed_result.substep_timings

            # Use cleaned text if available, otherwise original
            if postprocessed_result.structured_data.get("original_text"):
                final_text = postprocessed_result.structured_data["original_text"]
            elif google_ai_result:
                final_text = google_ai_result.text

            confidence = min(google_ai_result.confidence + 0.1, 1.0) if google_ai_result else 0.9
            processor_used = f"{google_ai_result.processor_used}_+_gpt_postprocessing" if google_ai_result else "gpt_postprocessing"

        elif google_ai_result and google_ai_result.success:
            # Use Google Document AI results
            final_text = google_ai_result.text
            confidence = google_ai_result.confidence
            processor_used = google_ai_result.processor_used
            entities = google_ai_result.entities
            tables = google_ai_result.tables
            form_fields = google_ai_result.form_fields
        elif extraction_result.success and extraction_result.text:
            # Use direct extraction results
            final_text = extraction_result.text
            confidence = 0.95  # High confidence for direct extraction
            processor_used = "direct_extraction"
        else:
            # Fallback
            final_text = extraction_result.error_message or "No text could be extracted"
            confidence = 0.0
            processor_used = "fallback"

        # Calculate processing time
        processing_time_ms = (time.time() - start_time) * 1000

        # Create comprehensive response
        response = ProcessingResponse(
            success=bool(final_text and confidence > 0),
            extracted_text=final_text,
            confidence=confidence,
            document_type=document_analysis.document_type if document_analysis else format_info.format_type,
            processor_used=processor_used,
            processing_time_ms=processing_time_ms,
            entities=entities,
            tables=tables,
            form_fields=form_fields,
            metadata={
                "request_id": request_id,
                "filename": filename,
                "file_size": len(file_data),
                "text_length": len(final_text),
                "timestamp": datetime.now().isoformat(),
                "processing_config": processing_config.dict(),
                "preprocessing_info": {
                    "applied": preprocessing_result.success if preprocessing_result else False,
                    "techniques": preprocessing_result.techniques_applied if preprocessing_result else [],
                    "quality_improvement": preprocessing_result.quality_improvement_estimate if preprocessing_result else 0.0,
                    "processing_time": preprocessing_result.processing_time if preprocessing_result else 0.0
                } if preprocessing_result else None
            },
            format_info=format_info.__dict__,
            document_analysis=document_analysis.__dict__ if document_analysis else None,
            processing_strategy=processing_strategy.__dict__ if processing_strategy else None,
            postprocessed_data=postprocessed_data,
            content_regions=content_regions,
            drawings_metadata=drawings_metadata,
            step_timings=step_timings,
            gpt_substep_timings=gpt_substep_timings,
            debug_info={
                "extraction_result": extraction_result.__dict__,
                "google_ai_result": {
                    "success": google_ai_result.success,
                    "text": google_ai_result.text,
                    "confidence": google_ai_result.confidence,
                    "processor_used": google_ai_result.processor_used,
                    "processing_time": google_ai_result.processing_time,
                    "page_count": google_ai_result.page_count,
                    "entities": google_ai_result.entities,
                    "tables": google_ai_result.tables,
                    "form_fields": google_ai_result.form_fields,
                    "error_message": google_ai_result.error_message,
                    "raw_response": None  # Exclude raw response to avoid serialization issues
                } if google_ai_result else None,
                "gpt_postprocessing_result": {
                    "success": postprocessed_result.success,
                    "processing_time": postprocessed_result.processing_time,
                    "confidence": postprocessed_result.confidence,
                    "content_regions_count": len(postprocessed_result.content_regions),
                    "drawings_count": len(postprocessed_result.drawings_metadata),
                    "structured_data_keys": list(postprocessed_result.structured_data.keys()) if postprocessed_result.structured_data else [],
                    "error_message": postprocessed_result.error_message
                } if postprocessed_result else None
            } if processing_config.debug_mode else None
        )

        logger.info(f"Processing completed for {filename}: {confidence:.3f} confidence, {processing_time_ms:.1f}ms")

        return response

    except HTTPException:
        raise
    except Exception as e:
        processing_time_ms = (time.time() - start_time) * 1000
        error_message = f"Processing error: {str(e)}"

        logger.error(f"Processing failed for {filename}: {error_message}")
        logger.error(traceback.format_exc())

        return ProcessingResponse(
            success=False,
            extracted_text="",
            confidence=0.0,
            document_type="unknown",
            processor_used="error",
            processing_time_ms=processing_time_ms,
            metadata={
                "request_id": request_id,
                "filename": filename,
                "timestamp": datetime.now().isoformat(),
                "error": error_message
            },
            format_info={},
            error_message=error_message
        )

@app.get("/api/v1/config", response_model=ConfigResponse)
async def get_config():
    """
    Get comprehensive API configuration

    Returns detailed information about:
    - Supported file formats (30+ formats)
    - Available processors and capabilities
    - System features and settings
    - Processing options and limits
    """

    return ConfigResponse(
        supported_formats=settings.SUPPORTED_FORMATS,
        processors={
            "google_document_ai": {
                "available": google_ai_hub.client is not None,
                "processors": settings.GOOGLE_PROCESSORS,
                "capabilities": settings.PROCESSOR_CAPABILITIES
            },
            "llm_router": {
                "available": intelligent_router.llm_client is not None,
                "model": settings.LLM_MODEL,
                "enabled": settings.LLM_ROUTING_ENABLED
            }
        },
        features={
            "llm_intelligent_routing": settings.LLM_ROUTING_ENABLED,
            "google_document_ai": google_ai_hub.client is not None,
            "universal_format_support": True,
            "parallel_processing": settings.PARALLEL_PROCESSING,
            "insurance_optimization": True,
            "advanced_ocr_challenges": True,
            "quality_assessment": True,
            "batch_processing": True,
            "comprehensive_debugging": True,
            "multi_language_support": True,
            "handwriting_recognition": True,
            "table_extraction": True,
            "form_processing": True,
            "entity_extraction": True
        },
        settings={
            "max_file_size": settings.MAX_FILE_SIZE,
            "max_workers": settings.MAX_WORKERS,
            "confidence_threshold": settings.DEFAULT_CONFIDENCE_THRESHOLD,
            "batch_size": settings.BATCH_SIZE,
            "cache_enabled": settings.ENABLE_CACHE,
            "debug_mode": settings.DEBUG_MODE
        }
    )

@app.get("/api/v1/stats")
async def get_processing_stats():
    """
    Get processing statistics and performance metrics

    Returns:
    - Document processing statistics
    - Processor usage metrics
    - Performance data
    - Cache statistics
    """

    try:
        router_stats = intelligent_router.get_processing_statistics()
        processor_status = google_ai_hub.get_processor_status()

        return {
            "routing_statistics": router_stats,
            "processor_status": processor_status,
            "system_info": {
                "version": "2.0.0",
                "uptime": "N/A",  # Could be implemented with startup time tracking
                "supported_formats": len(settings.SUPPORTED_FORMATS),
                "available_processors": len(settings.GOOGLE_PROCESSORS)
            },
            "performance_metrics": {
                "average_processing_time": "N/A",  # Could be implemented with metrics collection
                "success_rate": "N/A",
                "cache_hit_rate": "N/A"
            }
        }

    except Exception as e:
        logger.error(f"Error getting stats: {str(e)}")
        return {"error": str(e)}

@app.post("/api/v1/batch-process")
async def batch_process_documents(
    files: List[UploadFile] = File(...),
    config: Optional[str] = Form(None)
):
    """
    Batch process multiple documents

    Process multiple documents in parallel with intelligent routing.
    Optimized for high-throughput document processing.

    Args:
        files: List of document files to process
        config: Optional JSON configuration for processing parameters

    Returns:
        List of processing results for each document
    """

    start_time = time.time()
    batch_id = str(uuid.uuid4())

    logger.info(f"Starting batch processing for {len(files)} documents (Batch ID: {batch_id})")

    try:
        # Parse configuration
        processing_config = ProcessingConfig()
        if config:
            try:
                config_dict = json.loads(config)
                processing_config = ProcessingConfig(**config_dict)
            except Exception as e:
                logger.warning(f"Invalid config provided, using defaults: {str(e)}")

        # Process documents in parallel
        results = []

        # Create tasks for parallel processing
        tasks = []
        for file in files:
            # Read file data
            file_data = await file.read()
            filename = file.filename or f"document_{len(tasks)}"

            # Create processing task
            task = _process_single_document(file_data, filename, processing_config)
            tasks.append(task)

        # Execute all tasks in parallel
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        for i, result in enumerate(batch_results):
            if isinstance(result, Exception):
                results.append({
                    "success": False,
                    "filename": files[i].filename or f"document_{i}",
                    "error": str(result)
                })
            else:
                results.append(result)

        processing_time_ms = (time.time() - start_time) * 1000

        logger.info(f"Batch processing completed: {len(results)} documents, {processing_time_ms:.1f}ms")

        return {
            "success": True,
            "batch_id": batch_id,
            "total_documents": len(files),
            "processing_time_ms": processing_time_ms,
            "results": results,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        processing_time_ms = (time.time() - start_time) * 1000
        error_message = f"Batch processing error: {str(e)}"

        logger.error(f"Batch processing failed: {error_message}")

        return {
            "success": False,
            "batch_id": batch_id,
            "error": error_message,
            "processing_time_ms": processing_time_ms,
            "timestamp": datetime.now().isoformat()
        }

async def _process_single_document(file_data: bytes, filename: str, config: ProcessingConfig) -> Dict[str, Any]:
    """Helper function to process a single document in batch mode"""

    try:
        # Use the same processing logic as the main endpoint
        format_info = await universal_format_engine.detect_format(file_data, filename)
        extraction_result = await universal_format_engine.extract_content(file_data, filename)

        # Handle OCR processing based on configuration
        if extraction_result.requires_ocr or format_info.format_type == "image":

            if config.llm_routing_enabled:
                # LLM-routed processing
                document_analysis = await intelligent_router.analyze_document(file_data, filename)
                processing_strategy = await intelligent_router.create_processing_strategy(document_analysis)
                google_ai_result = await google_ai_hub.process_document(file_data, processing_strategy, filename)

                if google_ai_result and google_ai_result.success:
                    return {
                        "success": True,
                        "filename": filename,
                        "extracted_text": google_ai_result.text,
                        "confidence": google_ai_result.confidence,
                        "processor_used": google_ai_result.processor_used,
                        "document_type": document_analysis.document_type
                    }

            elif config.ocr_engine:
                # Direct engine routing (same as main endpoint)
                ocr_result = await ocr_engine_router.process_with_engine(
                    file_data,
                    filename,
                    config.ocr_engine,
                    processor=config.google_processor,
                    psm_mode=config.tesseract_psm
                )

                if ocr_result and ocr_result.success:
                    return {
                        "success": True,
                        "filename": filename,
                        "extracted_text": ocr_result.text,
                        "confidence": ocr_result.confidence,
                        "processor_used": ocr_result.processor_used,
                        "document_type": format_info.format_type
                    }

        # Direct extraction result (for non-OCR files)
        return {
            "success": extraction_result.success,
            "filename": filename,
            "extracted_text": extraction_result.text,
            "confidence": 0.95 if extraction_result.success else 0.0,
            "processor_used": "direct_extraction",
            "document_type": format_info.format_type
        }

    except Exception as e:
        return {
            "success": False,
            "filename": filename,
            "error": str(e)
        }

# Additional utility endpoints

@app.post("/api/v1/clear-cache")
async def clear_cache():
    """
    Clear processing cache

    Clears the intelligent routing cache to free memory
    and ensure fresh analysis for subsequent requests.
    """

    try:
        intelligent_router.clear_cache()
        return {
            "success": True,
            "message": "Cache cleared successfully",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/api/v1/supported-formats")
async def get_supported_formats():
    """
    Get detailed information about supported file formats

    Returns comprehensive information about all supported formats
    including processing methods and capabilities.
    """

    format_categories = {
        "images": ["png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp", "svg"],
        "documents": ["pdf"],
        "office": ["docx", "doc", "xlsx", "xls", "pptx", "ppt", "pptm"],
        "text": ["txt", "csv", "json", "yaml", "yml", "rtf"],
        "web": ["html", "htm", "xml", "mhtml"],
        "email": ["msg", "eml"],
        "archives": ["zip", "rar", "7z", "tar", "gz"],
        "business_intelligence": ["pbix", "pbit"],
        "open_office": ["odt", "ods", "odp"]
    }

    return {
        "total_formats": len(settings.SUPPORTED_FORMATS),
        "supported_formats": settings.SUPPORTED_FORMATS,
        "categories": format_categories,
        "processing_methods": {
            "direct_extraction": ["txt", "csv", "docx", "xlsx", "pptx", "html", "json"],
            "ocr_required": ["png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp"],
            "hybrid_processing": ["pdf"],
            "archive_extraction": ["zip", "rar", "7z"]
        },
        "google_ai_processors": settings.GOOGLE_PROCESSORS
    }

# Error handlers

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Custom HTTP exception handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": exc.detail,
            "status_code": exc.status_code,
            "timestamp": datetime.now().isoformat()
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """General exception handler"""
    logger.error(f"Unhandled exception: {str(exc)}")
    logger.error(traceback.format_exc())

    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal server error",
            "timestamp": datetime.now().isoformat()
        }
    )

# Startup and shutdown events

@app.on_event("startup")
async def startup_event():
    """Application startup event"""
    logger.info("Starting Zurich OCR Engine")
    logger.info(f"Supported formats: {len(settings.SUPPORTED_FORMATS)}")
    logger.info(f"LLM routing: {'enabled' if settings.LLM_ROUTING_ENABLED else 'disabled'}")
    logger.info(f"Google Document AI: {'enabled' if google_ai_hub.client else 'disabled'}")
    logger.info(f"Parallel processing: {'enabled' if settings.PARALLEL_PROCESSING else 'disabled'}")
    logger.info("System ready for OCR processing")

@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event"""
    logger.info("Shutting down Zurich OCR Engine")
    intelligent_router.clear_cache()
    logger.info("Shutdown complete")

# Main application entry point
if __name__ == "__main__":
    uvicorn.run(
        "zurich_ocr.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
        access_log=True
    )
