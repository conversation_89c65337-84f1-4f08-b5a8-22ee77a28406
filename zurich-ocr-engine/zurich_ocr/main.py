"""
Zurich OCR Engine - Main FastAPI Application
Advanced OCR solution with LLM-powered intelligent routing
"""

import asyncio
import io
import logging
import time
import uuid
import json
from typing import Dict, Any, Optional, List, Union
import traceback
from datetime import datetime
from dataclasses import asdict

from fastapi import FastAPI, File, UploadFile, HTTPException, Form, BackgroundTasks, Depends
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

# Local imports - components
from .core.config import settings
from .core.intelligent_router import intelligent_router, DocumentAnalysis, ProcessingStrategy
from .core.google_document_ai_hub import google_ai_hub, ProcessingResult
from .core.universal_format_engine import universal_format_engine, FileFormatInfo, ExtractionResult
from .core.gpt_postprocessor import gpt_postprocessor
from .core.gpt_postprocessor_v2 import gpt_postprocessor_v2
from .core.mixed_content_analyzer import mixed_content_analyzer
from .core.preprocessing_engine import preprocessing_engine
from .core.ocr_engine_router import ocr_engine_router
from .core.intelligent_prompt_processor import IntelligentPromptProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize intelligent prompt processor
intelligent_prompt_processor = IntelligentPromptProcessor()

# Helper functions for document processing
def _convert_ocr_to_google_result(ocr_result):
    """Convert OCR result to Google AI result format for compatibility"""
    from zurich_ocr.core.google_document_ai_hub import ProcessingResult

    if ocr_result.success:
        return ProcessingResult(
            success=True,
            text=ocr_result.text,
            confidence=ocr_result.confidence,
            processor_used=ocr_result.processor_used,
            processing_time=ocr_result.processing_time,
            page_count=1,
            entities=[],
            tables=[],
            form_fields=[]
        )
    else:
        return ProcessingResult(
            success=False,
            text="",
            confidence=0.0,
            processor_used=ocr_result.processor_used,
            processing_time=ocr_result.processing_time,
            page_count=0,
            entities=[],
            tables=[],
            form_fields=[],
            error_message=ocr_result.error_message
        )

async def _tesseract_fallback(file_data: bytes, filename: str):
    """Tesseract OCR fallback for when other methods fail"""
    try:
        import pytesseract
        from PIL import Image
        import io

        image = Image.open(io.BytesIO(file_data))
        tesseract_text = pytesseract.image_to_string(image, config='--psm 6')

        if tesseract_text.strip():
            from zurich_ocr.core.google_document_ai_hub import ProcessingResult
            result = ProcessingResult(
                success=True,
                text=tesseract_text.strip(),
                confidence=0.8,  # Reasonable confidence for Tesseract
                processor_used="tesseract_fallback",
                processing_time=0.5,
                page_count=1,
                entities=[],
                tables=[],
                form_fields=[]
            )
            logger.info(f"Tesseract OCR fallback successful for {filename}")
            return result
        else:
            raise Exception("Tesseract returned empty text")

    except Exception as e:
        logger.warning(f"Tesseract OCR fallback failed for {filename}: {str(e)}")
        from zurich_ocr.core.google_document_ai_hub import ProcessingResult
        return ProcessingResult(
            success=False,
            text="",
            confidence=0.0,
            processor_used="tesseract_fallback",
            processing_time=0.0,
            page_count=0,
            entities=[],
            tables=[],
            form_fields=[],
            error_message=f"Tesseract fallback failed: {str(e)}"
        )

async def _process_office_text_document(file_data: bytes, filename: str, document_category: str, extraction_result, config):
    """Process office and text documents with proper fallback chains"""

    if document_category == "office":
        # Office documents: Google Document AI digital + layout parser → Gemini → Tesseract
        try:
            # Try Google Document AI digital parsing first
            from .core.intelligent_router import ProcessingStrategy
            digital_strategy = ProcessingStrategy(
                primary_processor="LAYOUT_PARSER_PROCESSOR",
                enhancement_processors=[], preprocessing_steps=[], confidence_threshold=0.8,
                fallback_strategy=[], parallel_processing=False, batch_processing=False
            )

            google_ai_result = await google_ai_hub.process_document(file_data, digital_strategy, filename)

            if google_ai_result.success:
                return google_ai_result
            else:
                raise Exception(f"Google Document AI failed: {google_ai_result.error_message}")

        except Exception as e:
            # Fallback to Gemini Vision if configured
            if config.ocr_engine and config.ocr_engine.startswith("gemini"):
                try:
                    ocr_result = await ocr_engine_router.process_with_engine(
                        file_data, filename, config.ocr_engine,
                        processor=config.google_processor, psm_mode=config.tesseract_psm
                    )

                    if ocr_result.success:
                        return _convert_ocr_to_google_result(ocr_result)
                    else:
                        raise Exception(f"Gemini Vision failed: {ocr_result.error_message}")

                except Exception:
                    pass

            # Final fallback to Tesseract
            return await _tesseract_fallback(file_data, filename)

    elif document_category == "text":
        # Text documents: Native digital parsers → Gemini → Tesseract

        # Try direct extraction first (native digital parsing)
        if extraction_result.success and extraction_result.text.strip():
            from zurich_ocr.core.google_document_ai_hub import ProcessingResult
            return ProcessingResult(
                success=True, text=extraction_result.text, confidence=0.95,
                processor_used="native_digital_parser", processing_time=0.1,
                page_count=1, entities=[], tables=[], form_fields=[]
            )
        else:
            # Fallback to Gemini Vision if configured
            if config.ocr_engine and config.ocr_engine.startswith("gemini"):
                try:
                    ocr_result = await ocr_engine_router.process_with_engine(
                        file_data, filename, config.ocr_engine,
                        processor=config.google_processor, psm_mode=config.tesseract_psm
                    )

                    if ocr_result.success:
                        return _convert_ocr_to_google_result(ocr_result)
                    else:
                        raise Exception(f"Gemini Vision failed: {ocr_result.error_message}")

                except Exception:
                    pass

            # Final fallback to Tesseract
            return await _tesseract_fallback(file_data, filename)

async def _process_office_text_document_batch(file_data: bytes, filename: str, document_category: str, extraction_result, config):
    """Process office and text documents in batch mode with proper fallback chains (same logic as main endpoint)"""

    if document_category == "office":
        # Office documents: Google Document AI digital + layout parser → Gemini → Tesseract
        try:
            # Try Google Document AI digital parsing first
            from .core.intelligent_router import ProcessingStrategy
            digital_strategy = ProcessingStrategy(
                primary_processor="LAYOUT_PARSER_PROCESSOR",  # Best for office documents
                enhancement_processors=[], preprocessing_steps=[], confidence_threshold=0.8,
                fallback_strategy=[], parallel_processing=False, batch_processing=False
            )

            google_ai_result = await google_ai_hub.process_document(file_data, digital_strategy, filename)

            if google_ai_result.success:
                return google_ai_result
            else:
                raise Exception(f"Google Document AI failed: {google_ai_result.error_message}")

        except Exception as e:
            # Fallback to Gemini Vision if configured
            if config.ocr_engine and config.ocr_engine.startswith("gemini"):
                try:
                    ocr_result = await ocr_engine_router.process_with_engine(
                        file_data, filename, config.ocr_engine,
                        processor=config.google_processor, psm_mode=config.tesseract_psm
                    )

                    if ocr_result.success:
                        return _convert_ocr_to_google_result(ocr_result)
                    else:
                        raise Exception(f"Gemini Vision failed: {ocr_result.error_message}")

                except Exception:
                    pass

            # Final fallback to Tesseract
            return await _tesseract_fallback(file_data, filename)

    elif document_category == "text":
        # Text documents: Native digital parsers → Gemini → Tesseract

        # Try direct extraction first (native digital parsing)
        if extraction_result.success and extraction_result.text.strip():
            from zurich_ocr.core.google_document_ai_hub import ProcessingResult
            return ProcessingResult(
                success=True, text=extraction_result.text, confidence=0.95,
                processor_used="native_digital_parser", processing_time=0.1,
                page_count=1, entities=[], tables=[], form_fields=[]
            )
        else:
            # Fallback to Gemini Vision if configured
            if config.ocr_engine and config.ocr_engine.startswith("gemini"):
                try:
                    ocr_result = await ocr_engine_router.process_with_engine(
                        file_data, filename, config.ocr_engine,
                        processor=config.google_processor, psm_mode=config.tesseract_psm
                    )

                    if ocr_result.success:
                        return _convert_ocr_to_google_result(ocr_result)
                    else:
                        raise Exception(f"Gemini Vision failed: {ocr_result.error_message}")

                except Exception:
                    pass

            # Final fallback to Tesseract
            return await _tesseract_fallback(file_data, filename)

# FastAPI application
app = FastAPI(
    title="Zurich OCR Engine",
    description="""
    Advanced OCR processing with LLM-powered intelligent routing.

    Features:
    - GPT-4 powered intelligent document routing
    - Google Document AI integration with 40+ specialized processors
    - Universal file format support (30+ formats)
    - Advanced OCR challenge resolution
    - Insurance industry optimization
    - High-performance parallel processing
    - Comprehensive quality assessment
    """,
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for API requests and responses
class ProcessingConfig(BaseModel):
    """Configuration for document processing"""
    llm_routing_enabled: bool = Field(default=True, description="Enable LLM-powered intelligent routing")
    parallel_processing: bool = Field(default=True, description="Enable parallel processing for complex documents")
    confidence_threshold: float = Field(default=0.85, description="Minimum confidence threshold for results")
    enable_fallbacks: bool = Field(default=True, description="Enable fallback processing strategies")
    debug_mode: bool = Field(default=False, description="Enable detailed debug information")
    insurance_optimization: bool = Field(default=True, description="Enable insurance industry optimizations")

    # OCR Engine Selection (when LLM routing disabled)
    ocr_engine: Optional[str] = Field(default=None, description="OCR engine: 'gemini', 'gemini1.5', 'gemini2.5pro', 'gemini2.5flash', 'google', or 'tesseract'")
    google_processor: Optional[str] = Field(default="OCR_PROCESSOR", description="Google processor type")
    tesseract_psm: Optional[int] = Field(default=6, description="Tesseract PSM mode")

    # Post-processing Configuration
    post_processing: Union[str, bool] = Field(default="v1", description="Post-processing: 'v1', 'v2', or False")

    # Preprocessing Configuration
    preprocessing: Union[str, bool] = Field(default="auto", description="Preprocessing: True, False, 'auto', 'minimal', 'basic', 'enhanced', 'aggressive'")
    preprocessing_force_level: Optional[str] = Field(default=None, description="Override AI preprocessing decision")
    preprocessing_techniques: Optional[Dict[str, Union[bool, str]]] = Field(default=None, description="Fine-grained preprocessing control")

class ProcessingResponse(BaseModel):
    """Comprehensive processing response"""
    success: bool
    extracted_text: str
    confidence: float
    document_type: str
    processor_used: str
    processing_time_ms: float

    # Structured data
    entities: List[Dict[str, Any]] = []
    tables: List[Dict[str, Any]] = []
    form_fields: List[Dict[str, Any]] = []

    # Metadata
    metadata: Dict[str, Any]
    format_info: Dict[str, Any]

    # Analysis results
    document_analysis: Optional[Dict[str, Any]] = None
    processing_strategy: Optional[Dict[str, Any]] = None

    # GPT Post-processing results
    postprocessed_data: Optional[Dict[str, Any]] = None
    content_regions: Optional[List[Dict[str, Any]]] = None
    drawings_metadata: Optional[List[Dict[str, Any]]] = None

    # Step-by-step timing information
    step_timings: Optional[Dict[str, float]] = None
    gpt_substep_timings: Optional[Dict[str, float]] = None

    # Debug information
    debug_info: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None

class HealthResponse(BaseModel):
    """Health check response"""
    status: str
    timestamp: str
    version: str
    components: Dict[str, Any]

class ConfigResponse(BaseModel):
    """Configuration response"""
    supported_formats: List[str]
    processors: Dict[str, Any]
    features: Dict[str, Any]
    settings: Dict[str, Any]

# API Endpoints

@app.get("/api/v1/health", response_model=HealthResponse)
async def health_check():
    """
    Comprehensive health check endpoint

    Returns detailed status of all system components including:
    - LLM routing system
    - Google Document AI integration
    - Universal format engine
    - Processing statistics
    """

    try:
        # Check component health
        components = {
            "llm_router": {
                "status": "healthy" if intelligent_router.llm_client else "disabled",
                "statistics": intelligent_router.get_processing_statistics()
            },
            "google_ai_hub": {
                "status": "healthy" if google_ai_hub.client else "disabled",
                "processor_status": google_ai_hub.get_processor_status()
            },
            "universal_format_engine": {
                "status": "healthy",
                "supported_formats": len(universal_format_engine.supported_formats)
            },
            "settings": {
                "llm_routing_enabled": settings.LLM_ROUTING_ENABLED,
                "parallel_processing": settings.PARALLEL_PROCESSING,
                "debug_mode": settings.DEBUG_MODE
            }
        }

        return HealthResponse(
            status="healthy",
            timestamp=datetime.now().isoformat(),
            version="2.0.0",
            components=components
        )

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return HealthResponse(
            status="unhealthy",
            timestamp=datetime.now().isoformat(),
            version="2.0.0",
            components={"error": str(e)}
        )

@app.get("/readiness", status_code=200)
async def readiness_check():
    """
    Readiness check endpoint for AWS ALB/API Gateway.

    This endpoint is used by AWS Application Load Balancer to determine
    if the service is ready to receive traffic. It performs a lightweight
    check to ensure the service is operational.
    """
    try:
        # Perform basic readiness checks
        checks = {
            "service": "zurich-ocr-engine",
            "status": "ready",
            "timestamp": datetime.now().isoformat(),
            "version": "2.0.0"
        }

        # Optional: Add basic component checks
        if hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY:
            checks["llm_configured"] = True
        else:
            checks["llm_configured"] = False

        return JSONResponse(content=checks)

    except Exception as e:
        logger.error(f"Readiness check failed: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={
                "service": "zurich-ocr-engine",
                "status": "not_ready",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@app.post("/api/v1/extract-text", response_model=ProcessingResponse)
async def extract_text(
    file: UploadFile = File(...),
    config: Optional[str] = Form(None)
):
    """
    Text extraction endpoint with LLM-powered intelligent routing

    This endpoint provides advanced OCR processing with:
    - GPT-4 powered document analysis and routing
    - Google Document AI integration with 40+ specialized processors
    - Universal file format support (30+ formats)
    - Advanced OCR challenge resolution
    - Insurance industry optimization
    - High-performance parallel processing

    Args:
        file: Document file to process
        config: Optional JSON configuration for processing parameters

    Returns:
        ProcessingResponse: Comprehensive processing result with extracted text,
        structured data, confidence scores, and detailed metadata
    """

    start_time = time.time()
    request_id = str(uuid.uuid4())
    filename = file.filename or f"document_{request_id[:8]}"

    # Initialize step timing tracker
    step_timings = {}
    step_start_time = start_time

    logger.info(f"Starting processing for {filename} (ID: {request_id})")

    try:
        # Parse configuration
        processing_config = ProcessingConfig()
        if config:
            try:
                config_dict = json.loads(config)
                processing_config = ProcessingConfig(**config_dict)
            except Exception as e:
                logger.warning(f"Invalid config provided, using defaults: {str(e)}")

        # Read file data
        file_data = await file.read()

        # Validate file size
        if len(file_data) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE} bytes"
            )

        # Step 1: Universal format detection and extraction
        logger.info(f"Detecting format for {filename}")
        step_start_time = time.time()
        format_info = await universal_format_engine.detect_format(file_data, filename)
        extraction_result = await universal_format_engine.extract_content(file_data, filename)
        step_timings["1_format_detection_and_extraction"] = (time.time() - step_start_time) * 1000

        # Step 1.5: Initialize preprocessing variables (actual preprocessing moved after LLM analysis)
        preprocessed_file_data = file_data
        preprocessing_result = None
        step_timings["1.5_preprocessing"] = 0.0  # Will be updated later

        # Step 2: Document type classification and routing decision
        document_analysis = None
        processing_strategy = None

        # Classify document types for specialized handling
        office_formats = {"xlsx", "docx", "pptx"}  # Office documents - use Google Document AI digital parsing
        text_formats = {"txt", "csv", "html", "rtf"}  # Text documents - use native digital parsers
        ocr_formats = {"png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp", "pdf"}  # OCR required formats

        document_category = None
        if format_info.extension.lower() in office_formats:
            document_category = "office"
        elif format_info.extension.lower() in text_formats:
            document_category = "text"
        elif format_info.extension.lower() in ocr_formats or format_info.format_type == "image":
            document_category = "ocr"
        else:
            document_category = "other"

        # Determine processing path based on document category and configuration
        if document_category in ["office", "text"]:
            # Office and text documents: Skip LLM routing, use specialized processing
            if processing_config.llm_routing_enabled:
                logger.info(f"Overriding LLM routing for {document_category} document: {format_info.extension}")

            step_timings["2_llm_routing_analysis"] = 0.0  # Skipped
            step_timings["2.5_preprocessing_update"] = 0.0  # Skipped
            document_analysis = None
            processing_strategy = None

        elif document_category == "ocr" and processing_config.llm_routing_enabled:
            # OCR documents with LLM routing enabled
            logger.info(f"Performing LLM analysis for {filename}")
            step_start_time = time.time()
            document_analysis = await intelligent_router.analyze_document(
                file_data, filename, {"format_info": format_info.__dict__}
            )
            processing_strategy = await intelligent_router.create_processing_strategy(document_analysis)
            step_timings["2_llm_routing_analysis"] = (time.time() - step_start_time) * 1000

            # Step 2.5: Preprocessing after LLM analysis (if LLM recommends it)
            if (processing_config.preprocessing and
                processing_config.preprocessing != False and
                processing_config.preprocessing != "none" and
                document_analysis and
                hasattr(document_analysis, 'preprocessing_strategy') and
                document_analysis.preprocessing_strategy):

                logger.info("Performing preprocessing based on LLM analysis")
                step_start_time = time.time()

                preprocessing_result = await preprocessing_engine.preprocess_image(
                    file_data,
                    level=processing_config.preprocessing,
                    format_info=format_info,
                    document_analysis=document_analysis,
                    force_techniques=processing_config.preprocessing_techniques.get("techniques") if processing_config.preprocessing_techniques else None
                )

                if preprocessing_result.success:
                    preprocessed_file_data = preprocessing_result.processed_image_data
                    logger.info(f"Preprocessing completed: {len(preprocessing_result.techniques_applied)} techniques, {preprocessing_result.quality_improvement_estimate:.1%} improvement")
                else:
                    logger.warning(f"Preprocessing failed: {preprocessing_result.error_message}")

                step_timings["2.5_preprocessing_update"] = (time.time() - step_start_time) * 1000
            else:
                step_timings["2.5_preprocessing_update"] = 0.0

        elif document_category == "ocr" and not processing_config.llm_routing_enabled and processing_config.ocr_engine:
            # OCR documents with direct engine routing
            logger.info(f"Direct OCR routing: {processing_config.ocr_engine}")
            step_timings["2_llm_routing_analysis"] = 0.0  # Skipped

            # Step 2.5: Preprocessing for direct OCR (if configured)
            if (processing_config.preprocessing and
                processing_config.preprocessing != False and
                processing_config.preprocessing != "none"):

                logger.info(f"Performing preprocessing for direct OCR: {processing_config.preprocessing}")
                step_start_time = time.time()

                preprocessing_result = await preprocessing_engine.preprocess_image(
                    file_data,
                    level=processing_config.preprocessing,
                    format_info=format_info,
                    document_analysis=None,
                    force_techniques=processing_config.preprocessing_techniques.get("techniques") if processing_config.preprocessing_techniques else None
                )

                if preprocessing_result.success:
                    preprocessed_file_data = preprocessing_result.processed_image_data
                    logger.info(f"Preprocessing completed: {len(preprocessing_result.techniques_applied)} techniques, {preprocessing_result.quality_improvement_estimate:.1%} improvement")
                else:
                    logger.warning(f"Preprocessing failed: {preprocessing_result.error_message}")

                step_timings["2.5_preprocessing_update"] = (time.time() - step_start_time) * 1000
            else:
                step_timings["2.5_preprocessing_update"] = 0.0

            document_analysis = None
            processing_strategy = None
        else:
            # Other cases
            step_timings["2_llm_routing_analysis"] = 0.0  # Skipped
            step_timings["2.5_preprocessing_update"] = 0.0  # Skipped

        # Step 3: Document Processing based on category
        google_ai_result = None
        step_start_time = time.time()

        if document_category == "office":
            # Office documents: Google Document AI digital + layout parser → Gemini → Tesseract
            logger.info(f"Processing office document with Google Document AI digital parsing: {filename}")

            try:
                # Try Google Document AI digital parsing first
                from .core.intelligent_router import ProcessingStrategy
                digital_strategy = ProcessingStrategy(
                    primary_processor="LAYOUT_PARSER_PROCESSOR",  # Best for office documents
                    enhancement_processors=[],
                    preprocessing_steps=[],
                    confidence_threshold=0.8,
                    fallback_strategy=[],
                    parallel_processing=False,
                    batch_processing=False
                )

                google_ai_result = await google_ai_hub.process_document(
                    file_data,  # Use original file data for office documents
                    digital_strategy,
                    filename
                )

                if google_ai_result.success:
                    logger.info(f"Google Document AI digital parsing successful for {filename}")
                else:
                    raise Exception(f"Google Document AI failed: {google_ai_result.error_message}")

            except Exception as e:
                logger.warning(f"Google Document AI digital parsing failed for {filename}: {str(e)}")

                # Fallback to Gemini Vision if configured
                if processing_config.ocr_engine and processing_config.ocr_engine.startswith("gemini"):
                    logger.info(f"Trying Gemini Vision fallback for office document: {filename}")
                    try:
                        ocr_result = await ocr_engine_router.process_with_engine(
                            file_data,  # Use original file data
                            filename,
                            processing_config.ocr_engine,
                            processor=processing_config.google_processor,
                            psm_mode=processing_config.tesseract_psm
                        )

                        if ocr_result.success:
                            google_ai_result = _convert_ocr_to_google_result(ocr_result)
                            logger.info(f"Gemini Vision fallback successful for {filename}")
                        else:
                            raise Exception(f"Gemini Vision failed: {ocr_result.error_message}")

                    except Exception as gemini_e:
                        logger.warning(f"Gemini Vision fallback failed for {filename}: {str(gemini_e)}")
                        google_ai_result = None

                # Final fallback to Tesseract
                if not google_ai_result or not google_ai_result.success:
                    logger.info(f"Trying Tesseract fallback for office document: {filename}")
                    google_ai_result = await _tesseract_fallback(file_data, filename)

        elif document_category == "text":
            # Text documents: Native digital parsers → Gemini → Tesseract
            logger.info(f"Processing text document with native digital parsing: {filename}")

            # Try direct extraction first (native digital parsing)
            if extraction_result.success and extraction_result.text.strip():
                from zurich_ocr.core.google_document_ai_hub import ProcessingResult
                google_ai_result = ProcessingResult(
                    success=True,
                    text=extraction_result.text,
                    confidence=0.95,
                    processor_used="native_digital_parser",
                    processing_time=0.1,
                    page_count=1,
                    entities=[],
                    tables=[],
                    form_fields=[]
                )
                logger.info(f"Native digital parsing successful for {filename}")
            else:
                # Fallback to Gemini Vision if configured
                if processing_config.ocr_engine and processing_config.ocr_engine.startswith("gemini"):
                    logger.info(f"Trying Gemini Vision fallback for text document: {filename}")
                    try:
                        ocr_result = await ocr_engine_router.process_with_engine(
                            file_data,  # Use original file data
                            filename,
                            processing_config.ocr_engine,
                            processor=processing_config.google_processor,
                            psm_mode=processing_config.tesseract_psm
                        )

                        if ocr_result.success:
                            google_ai_result = _convert_ocr_to_google_result(ocr_result)
                            logger.info(f"Gemini Vision fallback successful for {filename}")
                        else:
                            raise Exception(f"Gemini Vision failed: {ocr_result.error_message}")

                    except Exception as gemini_e:
                        logger.warning(f"Gemini Vision fallback failed for {filename}: {str(gemini_e)}")
                        google_ai_result = None

                # Final fallback to Tesseract
                if not google_ai_result or not google_ai_result.success:
                    logger.info(f"Trying Tesseract fallback for text document: {filename}")
                    google_ai_result = await _tesseract_fallback(file_data, filename)

        elif document_category == "ocr":
            # OCR documents: Follow LLM routing or direct OCR routing
            if processing_strategy and (extraction_result.requires_ocr or format_info.format_type == "image"):
                # LLM-routed processing
                logger.info(f"Processing with Google Document AI using {processing_strategy.primary_processor}")
                google_ai_result = await google_ai_hub.process_document(
                    preprocessed_file_data, processing_strategy, filename
                )

                # If Google Document AI fails, fallback to Gemini Vision for LLM routing
                if not google_ai_result or not google_ai_result.success:
                    logger.warning(f"Google Document AI failed for LLM routing, trying Gemini Vision fallback")
                    try:
                        # Use Gemini 2.5 Pro for high-quality LLM routing
                        ocr_result = await ocr_engine_router.process_with_engine(
                            preprocessed_file_data,
                            filename,
                            "gemini2.5pro",  # Use high-quality model for LLM routing
                            processor=processing_config.google_processor,
                            psm_mode=processing_config.tesseract_psm
                        )

                        if ocr_result.success:
                            google_ai_result = _convert_ocr_to_google_result(ocr_result)
                            logger.info(f"Gemini Vision fallback successful for LLM routing: {filename}")
                        else:
                            logger.warning(f"Gemini Vision fallback also failed: {ocr_result.error_message}")

                    except Exception as e:
                        logger.warning(f"Gemini Vision fallback error: {str(e)}")
                        google_ai_result = None

            elif not processing_config.llm_routing_enabled and processing_config.ocr_engine and (extraction_result.requires_ocr or format_info.format_type == "image"):
                # Direct engine routing
                logger.info(f"Direct OCR processing with {processing_config.ocr_engine}")

                ocr_result = await ocr_engine_router.process_with_engine(
                    preprocessed_file_data,
                    filename,
                    processing_config.ocr_engine,
                    processor=processing_config.google_processor,
                    psm_mode=processing_config.tesseract_psm
                )

                # Convert OCR result to Google AI result format for compatibility
                google_ai_result = _convert_ocr_to_google_result(ocr_result)

        else:
            # Other document types - try direct extraction
            if extraction_result.success and extraction_result.text.strip():
                from zurich_ocr.core.google_document_ai_hub import ProcessingResult
                google_ai_result = ProcessingResult(
                    success=True,
                    text=extraction_result.text,
                    confidence=0.95,
                    processor_used="direct_extraction",
                    processing_time=0.1,
                    page_count=1,
                    entities=[],
                    tables=[],
                    form_fields=[]
                )

        step_timings["3_ocr_processing"] = (time.time() - step_start_time) * 1000

        # Error handling: If all processing methods failed, return error
        if not google_ai_result or not google_ai_result.success:
            error_message = f"All processing methods failed for {filename}"
            logger.error(error_message)

            from zurich_ocr.core.google_document_ai_hub import ProcessingResult
            google_ai_result = ProcessingResult(
                success=False,
                text="",
                confidence=0.0,
                processor_used="error",
                processing_time=0.0,
                page_count=0,
                entities=[],
                tables=[],
                form_fields=[],
                error_message=error_message
            )

        # Step 4: GPT Post-Processing (if enabled)
        postprocessed_result = None

        if (processing_config.post_processing and
            processing_config.post_processing != False and
            processing_config.post_processing != "none" and
            google_ai_result and google_ai_result.success):

            logger.info(f"Starting GPT post-processing V{processing_config.post_processing} for {filename}")
            step_start_time = time.time()

            # Prepare Google Document AI output for post-processing
            raw_ocr_output = {
                'text': google_ai_result.text,
                'entities': google_ai_result.entities,
                'tables': google_ai_result.tables,
                'form_fields': google_ai_result.form_fields,
                'confidence': google_ai_result.confidence,
                'processor_used': google_ai_result.processor_used,
                'page_count': google_ai_result.page_count
            }

            # Determine document type for specialized processing
            doc_type = "insurance"  # Default
            if document_analysis:
                doc_type = document_analysis.document_type

            # Choose post-processing version
            if processing_config.post_processing == "v2":
                # V2 Speed-optimized post-processing
                postprocessed_result_v2 = await gpt_postprocessor_v2.process_document(
                    raw_ocr_output, doc_type
                )

                # Convert V2 result to V1 format for compatibility
                if postprocessed_result_v2.success:
                    from zurich_ocr.core.gpt_postprocessor import ProcessedContent, ContentRegion
                    postprocessed_result = ProcessedContent(
                        structured_data=postprocessed_result_v2.structured_data,
                        content_regions=[],  # V2 doesn't generate content regions
                        drawings_metadata=[],  # V2 doesn't generate drawings metadata
                        processing_time=postprocessed_result_v2.processing_time,
                        confidence=postprocessed_result_v2.confidence,
                        success=True,
                        substep_timings=postprocessed_result_v2.substep_timings
                    )
                else:
                    from zurich_ocr.core.gpt_postprocessor import ProcessedContent
                    postprocessed_result = ProcessedContent(
                        structured_data={},
                        content_regions=[],
                        drawings_metadata=[],
                        processing_time=postprocessed_result_v2.processing_time,
                        confidence=0.0,
                        success=False,
                        error_message=postprocessed_result_v2.error_message,
                        substep_timings=postprocessed_result_v2.substep_timings
                    )
            else:
                # V1 Full-featured post-processing
                postprocessed_result = await gpt_postprocessor.process_document(
                    raw_ocr_output, doc_type
                )

            step_timings["4_gpt_postprocessing"] = (time.time() - step_start_time) * 1000
            logger.info(f"GPT post-processing V{processing_config.post_processing} completed in {postprocessed_result.processing_time:.1f}s")
        else:
            step_timings["4_gpt_postprocessing"] = 0.0  # Skipped

        # Step 5: Combine results
        final_text = ""
        confidence = 0.0
        processor_used = "direct_extraction"
        entities = []
        tables = []
        form_fields = []

        # Post-processing results
        postprocessed_data = None
        content_regions = None
        drawings_metadata = None
        gpt_substep_timings = None

        if postprocessed_result and postprocessed_result.success:
            # Use GPT post-processed results (highest quality)
            postprocessed_data = postprocessed_result.structured_data
            content_regions = [region.__dict__ for region in postprocessed_result.content_regions] if postprocessed_result.content_regions else None
            drawings_metadata = postprocessed_result.drawings_metadata
            gpt_substep_timings = postprocessed_result.substep_timings

            # Use cleaned text if available, otherwise original
            if postprocessed_result.structured_data.get("original_text"):
                final_text = postprocessed_result.structured_data["original_text"]
            elif google_ai_result:
                final_text = google_ai_result.text

            confidence = min(google_ai_result.confidence + 0.1, 1.0) if google_ai_result else 0.9
            processor_used = f"{google_ai_result.processor_used}_+_gpt_postprocessing" if google_ai_result else "gpt_postprocessing"

        elif google_ai_result and google_ai_result.success:
            # Use Google Document AI results
            final_text = google_ai_result.text
            confidence = google_ai_result.confidence
            processor_used = google_ai_result.processor_used
            entities = google_ai_result.entities
            tables = google_ai_result.tables
            form_fields = google_ai_result.form_fields
        elif extraction_result.success and extraction_result.text:
            # Use direct extraction results
            final_text = extraction_result.text
            confidence = 0.95  # High confidence for direct extraction
            processor_used = "direct_extraction"
        else:
            # Fallback
            final_text = extraction_result.error_message or "No text could be extracted"
            confidence = 0.0
            processor_used = "fallback"

        # Calculate processing time
        processing_time_ms = (time.time() - start_time) * 1000

        # Create comprehensive response
        response = ProcessingResponse(
            success=bool(final_text and confidence > 0),
            extracted_text=final_text,
            confidence=confidence,
            document_type=document_analysis.document_type if document_analysis else format_info.format_type,
            processor_used=processor_used,
            processing_time_ms=processing_time_ms,
            entities=entities,
            tables=tables,
            form_fields=form_fields,
            metadata={
                "request_id": request_id,
                "filename": filename,
                "file_size": len(file_data),
                "text_length": len(final_text),
                "timestamp": datetime.now().isoformat(),
                "processing_config": processing_config.dict(),
                "preprocessing_info": {
                    "applied": preprocessing_result.success if preprocessing_result else False,
                    "techniques": preprocessing_result.techniques_applied if preprocessing_result else [],
                    "quality_improvement": preprocessing_result.quality_improvement_estimate if preprocessing_result else 0.0,
                    "processing_time": preprocessing_result.processing_time if preprocessing_result else 0.0
                } if preprocessing_result else None
            },
            format_info=format_info.__dict__,
            document_analysis=document_analysis.__dict__ if document_analysis else None,
            processing_strategy=processing_strategy.__dict__ if processing_strategy else None,
            postprocessed_data=postprocessed_data,
            content_regions=content_regions,
            drawings_metadata=drawings_metadata,
            step_timings=step_timings,
            gpt_substep_timings=gpt_substep_timings,
            debug_info={
                "extraction_result": extraction_result.__dict__,
                "google_ai_result": {
                    "success": google_ai_result.success,
                    "text": google_ai_result.text,
                    "confidence": google_ai_result.confidence,
                    "processor_used": google_ai_result.processor_used,
                    "processing_time": google_ai_result.processing_time,
                    "page_count": google_ai_result.page_count,
                    "entities": google_ai_result.entities,
                    "tables": google_ai_result.tables,
                    "form_fields": google_ai_result.form_fields,
                    "error_message": google_ai_result.error_message,
                    "raw_response": None  # Exclude raw response to avoid serialization issues
                } if google_ai_result else None,
                "gpt_postprocessing_result": {
                    "success": postprocessed_result.success,
                    "processing_time": postprocessed_result.processing_time,
                    "confidence": postprocessed_result.confidence,
                    "content_regions_count": len(postprocessed_result.content_regions),
                    "drawings_count": len(postprocessed_result.drawings_metadata),
                    "structured_data_keys": list(postprocessed_result.structured_data.keys()) if postprocessed_result.structured_data else [],
                    "error_message": postprocessed_result.error_message
                } if postprocessed_result else None
            } if processing_config.debug_mode else None
        )

        logger.info(f"Processing completed for {filename}: {confidence:.3f} confidence, {processing_time_ms:.1f}ms")

        return response

    except HTTPException:
        raise
    except Exception as e:
        processing_time_ms = (time.time() - start_time) * 1000
        error_message = f"Processing error: {str(e)}"

        logger.error(f"Processing failed for {filename}: {error_message}")
        logger.error(traceback.format_exc())

        return ProcessingResponse(
            success=False,
            extracted_text="",
            confidence=0.0,
            document_type="unknown",
            processor_used="error",
            processing_time_ms=processing_time_ms,
            metadata={
                "request_id": request_id,
                "filename": filename,
                "timestamp": datetime.now().isoformat(),
                "error": error_message
            },
            format_info={},
            error_message=error_message
        )

@app.get("/api/v1/config", response_model=ConfigResponse)
async def get_config():
    """
    Get comprehensive API configuration

    Returns detailed information about:
    - Supported file formats (30+ formats)
    - Available processors and capabilities
    - System features and settings
    - Processing options and limits
    """

    return ConfigResponse(
        supported_formats=settings.SUPPORTED_FORMATS,
        processors={
            "google_document_ai": {
                "available": google_ai_hub.client is not None,
                "processors": settings.GOOGLE_PROCESSORS,
                "capabilities": settings.PROCESSOR_CAPABILITIES
            },
            "llm_router": {
                "available": intelligent_router.llm_client is not None,
                "model": settings.LLM_MODEL,
                "enabled": settings.LLM_ROUTING_ENABLED
            }
        },
        features={
            "llm_intelligent_routing": settings.LLM_ROUTING_ENABLED,
            "google_document_ai": google_ai_hub.client is not None,
            "universal_format_support": True,
            "parallel_processing": settings.PARALLEL_PROCESSING,
            "insurance_optimization": True,
            "advanced_ocr_challenges": True,
            "quality_assessment": True,
            "batch_processing": True,
            "comprehensive_debugging": True,
            "multi_language_support": True,
            "handwriting_recognition": True,
            "table_extraction": True,
            "form_processing": True,
            "entity_extraction": True
        },
        settings={
            "max_file_size": settings.MAX_FILE_SIZE,
            "max_workers": settings.MAX_WORKERS,
            "confidence_threshold": settings.DEFAULT_CONFIDENCE_THRESHOLD,
            "batch_size": settings.BATCH_SIZE,
            "cache_enabled": settings.ENABLE_CACHE,
            "debug_mode": settings.DEBUG_MODE
        }
    )

@app.get("/api/v1/stats")
async def get_processing_stats():
    """
    Get processing statistics and performance metrics

    Returns:
    - Document processing statistics
    - Processor usage metrics
    - Performance data
    - Cache statistics
    """

    try:
        router_stats = intelligent_router.get_processing_statistics()
        processor_status = google_ai_hub.get_processor_status()

        return {
            "routing_statistics": router_stats,
            "processor_status": processor_status,
            "system_info": {
                "version": "2.0.0",
                "uptime": "N/A",  # Could be implemented with startup time tracking
                "supported_formats": len(settings.SUPPORTED_FORMATS),
                "available_processors": len(settings.GOOGLE_PROCESSORS)
            },
            "performance_metrics": {
                "average_processing_time": "N/A",  # Could be implemented with metrics collection
                "success_rate": "N/A",
                "cache_hit_rate": "N/A"
            }
        }

    except Exception as e:
        logger.error(f"Error getting stats: {str(e)}")
        return {"error": str(e)}

@app.post("/api/v1/batch-process")
async def batch_process_documents(
    files: List[UploadFile] = File(...),
    config: Optional[str] = Form(None)
):
    """
    Batch process multiple documents

    Process multiple documents in parallel with intelligent routing.
    Optimized for high-throughput document processing.

    Args:
        files: List of document files to process
        config: Optional JSON configuration for processing parameters

    Returns:
        List of processing results for each document
    """

    start_time = time.time()
    batch_id = str(uuid.uuid4())

    logger.info(f"Starting batch processing for {len(files)} documents (Batch ID: {batch_id})")

    try:
        # Parse configuration
        processing_config = ProcessingConfig()
        if config:
            try:
                config_dict = json.loads(config)
                processing_config = ProcessingConfig(**config_dict)
            except Exception as e:
                logger.warning(f"Invalid config provided, using defaults: {str(e)}")

        # Process documents in parallel
        results = []

        # Create tasks for parallel processing using the same logic as single document endpoint
        tasks = []
        for i, file in enumerate(files):
            # Read file data
            file_data = await file.read()
            filename = file.filename or f"document_{i}"

            # Create processing task using the exact same logic as extract_text endpoint
            task = _process_document_batch(file_data, filename, processing_config)
            tasks.append(task)

        # Execute all tasks in parallel for maximum speed
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        for i, result in enumerate(batch_results):
            if isinstance(result, Exception):
                results.append({
                    "success": False,
                    "filename": files[i].filename or f"document_{i}",
                    "error": str(result)
                })
            else:
                results.append(result)

        processing_time_ms = (time.time() - start_time) * 1000

        logger.info(f"Batch processing completed: {len(results)} documents, {processing_time_ms:.1f}ms")

        return {
            "success": True,
            "batch_id": batch_id,
            "total_documents": len(files),
            "processing_time_ms": processing_time_ms,
            "results": results,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        processing_time_ms = (time.time() - start_time) * 1000
        error_message = f"Batch processing error: {str(e)}"

        logger.error(f"Batch processing failed: {error_message}")

        return {
            "success": False,
            "batch_id": batch_id,
            "error": error_message,
            "processing_time_ms": processing_time_ms,
            "timestamp": datetime.now().isoformat()
        }

async def _process_document_batch(file_data: bytes, filename: str, processing_config) -> Dict[str, Any]:
    """
    Process a single document in batch mode using EXACT same logic as main endpoint
    This ensures consistent results between single and batch processing
    """

    start_time = time.time()
    request_id = str(uuid.uuid4())[:8]  # Shorter ID for batch

    try:
        # Step 1: Universal format detection and extraction (SAME AS MAIN ENDPOINT)
        format_info = await universal_format_engine.detect_format(file_data, filename)
        extraction_result = await universal_format_engine.extract_content(file_data, filename)

        # Step 1.5: Initialize preprocessing variables (SAME AS MAIN ENDPOINT)
        preprocessed_file_data = file_data
        preprocessing_result = None

        # Step 2: Document type classification and routing decision (SAME AS MAIN ENDPOINT)
        office_formats = {"xlsx", "docx", "pptx"}  # Office documents - use Google Document AI digital parsing
        text_formats = {"txt", "csv", "html", "rtf"}  # Text documents - use native digital parsers
        ocr_formats = {"png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp", "pdf"}  # OCR required formats

        document_category = None
        if format_info.extension.lower() in office_formats:
            document_category = "office"
        elif format_info.extension.lower() in text_formats:
            document_category = "text"
        elif format_info.extension.lower() in ocr_formats or format_info.format_type == "image":
            document_category = "ocr"
        else:
            document_category = "other"

        document_analysis = None
        processing_strategy = None

        # Determine processing path based on document category and configuration (SAME AS MAIN ENDPOINT)
        if document_category in ["office", "text"]:
            # Office and text documents: Skip LLM routing, use specialized processing
            if processing_config.llm_routing_enabled:
                logger.info(f"Batch: Overriding LLM routing for {document_category} document: {format_info.extension}")

            google_ai_result = await _process_office_text_document_batch(
                file_data, filename, document_category, extraction_result, processing_config
            )

        elif document_category == "ocr" and processing_config.llm_routing_enabled:
            # OCR documents with LLM routing enabled (SAME AS MAIN ENDPOINT)
            document_analysis = await intelligent_router.analyze_document(
                file_data, filename, {"format_info": format_info.__dict__}
            )
            processing_strategy = await intelligent_router.create_processing_strategy(document_analysis)

            # Preprocessing after LLM analysis (if LLM recommends it) (SAME AS MAIN ENDPOINT)
            if (processing_config.preprocessing and processing_config.preprocessing != False and
                processing_config.preprocessing != "none" and document_analysis and
                hasattr(document_analysis, 'preprocessing_strategy') and document_analysis.preprocessing_strategy):

                preprocessing_result = await preprocessing_engine.preprocess_image(
                    file_data, level=processing_config.preprocessing, format_info=format_info,
                    document_analysis=document_analysis,
                    force_techniques=processing_config.preprocessing_techniques.get("techniques") if processing_config.preprocessing_techniques else None
                )

                if preprocessing_result.success:
                    preprocessed_file_data = preprocessing_result.processed_image_data

            # LLM-routed processing (SAME AS MAIN ENDPOINT)
            google_ai_result = await google_ai_hub.process_document(preprocessed_file_data, processing_strategy, filename)

        elif document_category == "ocr" and not processing_config.llm_routing_enabled and processing_config.ocr_engine:
            # OCR documents with direct engine routing (SAME AS MAIN ENDPOINT)

            # Preprocessing for direct OCR (if configured) (SAME AS MAIN ENDPOINT)
            if (processing_config.preprocessing and processing_config.preprocessing != False and
                processing_config.preprocessing != "none"):

                preprocessing_result = await preprocessing_engine.preprocess_image(
                    file_data, level=processing_config.preprocessing, format_info=format_info,
                    document_analysis=None,
                    force_techniques=processing_config.preprocessing_techniques.get("techniques") if processing_config.preprocessing_techniques else None
                )

                if preprocessing_result.success:
                    preprocessed_file_data = preprocessing_result.processed_image_data

            # Direct engine routing (SAME AS MAIN ENDPOINT)
            ocr_result = await ocr_engine_router.process_with_engine(
                preprocessed_file_data, filename, processing_config.ocr_engine,
                processor=processing_config.google_processor, psm_mode=processing_config.tesseract_psm
            )

            # Convert OCR result to Google AI result format for compatibility (SAME AS MAIN ENDPOINT)
            google_ai_result = _convert_ocr_to_google_result(ocr_result)

        else:
            # Other document types - try direct extraction (SAME AS MAIN ENDPOINT)
            if extraction_result.success and extraction_result.text.strip():
                from zurich_ocr.core.google_document_ai_hub import ProcessingResult
                google_ai_result = ProcessingResult(
                    success=True, text=extraction_result.text, confidence=0.95,
                    processor_used="direct_extraction", processing_time=0.1,
                    page_count=1, entities=[], tables=[], form_fields=[]
                )
            else:
                google_ai_result = None

        # Error handling: If all processing methods failed, return error (SAME AS MAIN ENDPOINT)
        if not google_ai_result or not google_ai_result.success:
            error_message = f"All processing methods failed for {filename}"

            from zurich_ocr.core.google_document_ai_hub import ProcessingResult
            google_ai_result = ProcessingResult(
                success=False, text="", confidence=0.0, processor_used="error",
                processing_time=0.0, page_count=0, entities=[], tables=[], form_fields=[],
                error_message=error_message
            )

        # Calculate processing time
        processing_time_ms = (time.time() - start_time) * 1000

        # Return simplified result for batch processing (but with same core data as main endpoint)
        return {
            "success": google_ai_result.success,
            "filename": filename,
            "extracted_text": google_ai_result.text,
            "confidence": google_ai_result.confidence,
            "processor_used": google_ai_result.processor_used,
            "document_type": document_analysis.document_type if document_analysis else format_info.format_type,
            "processing_time_ms": processing_time_ms,
            "request_id": request_id,
            "error_message": google_ai_result.error_message if not google_ai_result.success else None
        }

    except Exception as e:
        processing_time_ms = (time.time() - start_time) * 1000
        error_message = f"Batch processing error for {filename}: {str(e)}"

        logger.error(error_message)

        return {
            "success": False,
            "filename": filename,
            "extracted_text": "",
            "confidence": 0.0,
            "processor_used": "error",
            "document_type": "unknown",
            "processing_time_ms": processing_time_ms,
            "request_id": request_id,
            "error_message": error_message
        }

# Additional utility endpoints

@app.post("/api/v1/clear-cache")
async def clear_cache():
    """
    Clear processing cache

    Clears the intelligent routing cache to free memory
    and ensure fresh analysis for subsequent requests.
    """

    try:
        intelligent_router.clear_cache()
        return {
            "success": True,
            "message": "Cache cleared successfully",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/api/v1/supported-formats")
async def get_supported_formats():
    """
    Get detailed information about supported file formats

    Returns comprehensive information about all supported formats
    including processing methods and capabilities.
    """

    format_categories = {
        "images": ["png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp", "svg"],
        "documents": ["pdf"],
        "office": ["docx", "doc", "xlsx", "xls", "pptx", "ppt", "pptm"],
        "text": ["txt", "csv", "json", "yaml", "yml", "rtf"],
        "web": ["html", "htm", "xml", "mhtml"],
        "email": ["msg", "eml"],
        "archives": ["zip", "rar", "7z", "tar", "gz"],
        "business_intelligence": ["pbix", "pbit"],
        "open_office": ["odt", "ods", "odp"]
    }

    return {
        "total_formats": len(settings.SUPPORTED_FORMATS),
        "supported_formats": settings.SUPPORTED_FORMATS,
        "categories": format_categories,
        "processing_methods": {
            "direct_extraction": ["txt", "csv", "docx", "xlsx", "pptx", "html", "json"],
            "ocr_required": ["png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp"],
            "hybrid_processing": ["pdf"],
            "archive_extraction": ["zip", "rar", "7z"]
        },
        "google_ai_processors": settings.GOOGLE_PROCESSORS
    }

# Error handlers

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Custom HTTP exception handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": exc.detail,
            "status_code": exc.status_code,
            "timestamp": datetime.now().isoformat()
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """General exception handler"""
    logger.error(f"Unhandled exception: {str(exc)}")
    logger.error(traceback.format_exc())

    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal server error",
            "timestamp": datetime.now().isoformat()
        }
    )

@app.post("/api/v1/intelligent-extract", response_model=ProcessingResponse)
async def intelligent_extract_text(
    file: UploadFile = File(...),
    config: Optional[str] = Form(None)
):
    """
    Intelligent text extraction with advanced prompt processing

    This endpoint uses the new intelligent prompt processor that:
    - Automatically classifies document types across all Zurich categories
    - Applies optimized prompts based on document characteristics
    - Extracts structured data with domain-specific intelligence
    - Provides comprehensive confidence scoring and validation

    Supports all document types:
    - Claims: Travel, Motor, Disability (Canada, UK, Ecuador)
    - Underwriting: Mid Market, SME, Bond/Surety, Trade Credit, Professional Indemnity
    - Marketing: SEO Reports, Performance Analytics, Campaign Data
    - Finance: IFRS17 Reports, Contract Management, Procurement

    Args:
        file: Document file to process
        config: Optional JSON configuration for processing parameters

    Returns:
        ProcessingResponse: Enhanced processing result with intelligent extraction
    """

    start_time = time.time()
    request_id = str(uuid.uuid4())
    filename = file.filename or f"document_{request_id[:8]}"

    logger.info(f"Starting intelligent processing for {filename} (ID: {request_id})")

    try:
        # Parse configuration
        processing_config = ProcessingConfig()
        if config:
            try:
                config_dict = json.loads(config)
                processing_config = ProcessingConfig(**config_dict)
            except Exception as e:
                logger.warning(f"Invalid config provided, using defaults: {str(e)}")

        # Read file data
        file_data = await file.read()

        # Validate file size
        if len(file_data) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE} bytes"
            )

        # Step 1: Extract basic content for classification
        format_info = await universal_format_engine.detect_format(file_data, filename)
        extraction_result = await universal_format_engine.extract_content(file_data, filename)

        # Use extracted text or perform basic OCR for classification
        content_for_classification = extraction_result.text if extraction_result.success else ""

        if not content_for_classification and format_info.format_type == "image":
            # Quick OCR for classification if no text available
            try:
                ocr_result = await ocr_engine_router.process_with_engine(
                    file_data, filename, "tesseract",
                    processor="OCR_PROCESSOR", psm_mode=6
                )
                content_for_classification = ocr_result.text if ocr_result.success else ""
            except Exception:
                content_for_classification = ""

        # Step 2: Intelligent prompt processing
        ocr_confidence = 0.8  # Default confidence
        if extraction_result.success and hasattr(extraction_result, 'confidence'):
            ocr_confidence = extraction_result.confidence

        processing_result = await intelligent_prompt_processor.process_document(
            content=content_for_classification,
            filename=filename,
            ocr_confidence=ocr_confidence,
            processing_mode=processing_config.post_processing if isinstance(processing_config.post_processing, str) else "balanced",
            llm_client=gpt_postprocessor  # Use existing GPT client
        )

        if not processing_result.success:
            raise HTTPException(
                status_code=500,
                detail=f"Intelligent processing failed: {processing_result.error_message}"
            )

        # Step 3: Build comprehensive response
        processing_time = time.time() - start_time

        # Extract key information from intelligent processing result
        extracted_data = processing_result.extracted_data
        classification = processing_result.document_classification

        response = ProcessingResponse(
            success=True,
            extracted_text=extracted_data.get("extracted_text", content_for_classification),
            confidence=processing_result.confidence_scores.get("overall", 0.8),
            document_type=classification.document_type if classification else "unknown",
            processor_used=f"intelligent_prompt_processor_{classification.document_type if classification else 'general'}",
            processing_time_ms=processing_time * 1000,

            # Enhanced structured data from intelligent processing
            entities=[],  # Convert to list format expected by API
            tables=[],
            form_fields=[],

            # Metadata
            metadata={
                "file_size": len(file_data),
                "filename": filename,
                "request_id": request_id,
                "processing_mode": "intelligent",
                "classification_confidence": classification.confidence if classification else 0.5,
                "detected_patterns": classification.detected_patterns if classification else []
            },
            format_info=format_info.to_dict() if hasattr(format_info, 'to_dict') else {},

            # Intelligent processing specific data
            document_analysis={
                "classification": asdict(classification) if classification else {},
                "confidence_scores": processing_result.confidence_scores,
                "processing_intelligence": extracted_data.get("processing_intelligence", {})
            },
            processing_strategy={
                "template_used": "balanced_intelligent_prompt.tmpl",
                "document_category": classification.category if classification else "OTHER",
                "region": classification.region if classification else "Global"
            },

            # Post-processing results (from intelligent extraction)
            postprocessed_data=extracted_data.get("domain_specific_data", {}),

            # Step timings
            step_timings={
                "format_detection": 0.1,
                "content_extraction": 0.2,
                "intelligent_classification": processing_result.processing_time * 0.3 * 1000,
                "intelligent_extraction": processing_result.processing_time * 0.7 * 1000,
                "total": processing_time * 1000
            },

            # Debug information
            debug_info={
                "intelligent_processing": {
                    "classification_result": asdict(classification) if classification else {},
                    "confidence_breakdown": processing_result.confidence_scores,
                    "extracted_entities_count": len(extracted_data.get("key_entities", {})),
                    "processing_notes": extracted_data.get("quality_metrics", {}).get("processing_notes", [])
                }
            } if processing_config.debug_mode else None
        )

        logger.info(f"Intelligent processing completed for {filename} in {processing_time:.2f}s")
        return response

    except HTTPException:
        raise
    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Intelligent processing failed for {filename}: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")

        raise HTTPException(
            status_code=500,
            detail=f"Intelligent processing failed: {str(e)}"
        )

# Startup and shutdown events

@app.on_event("startup")
async def startup_event():
    """Application startup event"""
    logger.info("Starting Zurich OCR Engine")
    logger.info(f"Supported formats: {len(settings.SUPPORTED_FORMATS)}")
    logger.info(f"LLM routing: {'enabled' if settings.LLM_ROUTING_ENABLED else 'disabled'}")
    logger.info(f"Google Document AI: {'enabled' if google_ai_hub.client else 'disabled'}")
    logger.info(f"Parallel processing: {'enabled' if settings.PARALLEL_PROCESSING else 'disabled'}")
    logger.info("System ready for OCR processing")

@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event"""
    logger.info("Shutting down Zurich OCR Engine")
    intelligent_router.clear_cache()
    logger.info("Shutdown complete")

# Main application entry point
if __name__ == "__main__":
    uvicorn.run(
        "zurich_ocr.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
        access_log=True
    )
