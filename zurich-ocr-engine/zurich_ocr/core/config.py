import os
import json
from typing import List, Dict, Any, Optional

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass  # dotenv not available, continue without it

# Try to import BaseSettings from the correct location
try:
    from pydantic_settings import BaseSettings
except ImportError:
    try:
        from pydantic import BaseSettings
    except ImportError:
        # Fallback for older versions
        class BaseSettings:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

class Settings(BaseSettings):
    """OCR engine configuration with comprehensive settings"""

    # API Keys
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    GOOGLE_CREDENTIALS_JSON: str = os.getenv("GOOGLE_CREDENTIALS_JSON", "")
    GOOGLE_PROJECT_ID: str = os.getenv("GOOGLE_PROJECT_ID", "")
    GOOGLE_LOCATION: str = os.getenv("GOOGLE_LOCATION", "us")
    AWS_ACCESS_KEY_ID: str = os.getenv("AWS_ACCESS_KEY_ID", "")
    AWS_SECRET_ACCESS_KEY: str = os.getenv("AWS_SECRET_ACCESS_KEY", "")

    # Gemini Vision Configuration (uses service account via Vertex AI)
    GEMINI_MODEL: str = os.getenv("GEMINI_MODEL", "gemini-1.5-pro")
    GEMINI_TEMPERATURE: float = float(os.getenv("GEMINI_TEMPERATURE", "0.1"))
    GEMINI_MAX_TOKENS: int = int(os.getenv("GEMINI_MAX_TOKENS", "4000"))

    # Available Gemini Models for OCR
    GEMINI_MODELS: Dict[str, Dict[str, Any]] = {
        "gemini-1.5-pro": {
            "name": "gemini-1.5-pro",
            "description": "Gemini 1.5 Pro - High accuracy, good for complex documents",
            "strengths": ["complex_layouts", "high_accuracy", "multi_language"],
            "speed": "medium",
            "cost": "medium",
            "max_tokens": 8192
        },
        "gemini-1.5-flash": {
            "name": "gemini-1.5-flash",
            "description": "Gemini 1.5 Flash - Fast processing, good for simple documents",
            "strengths": ["speed", "simple_documents", "batch_processing"],
            "speed": "fast",
            "cost": "low",
            "max_tokens": 8192
        },
        "gemini-2.5-pro": {
            "name": "gemini-2.5-pro",
            "description": "Gemini 2.5 Pro - Latest model with advanced reasoning",
            "strengths": ["advanced_reasoning", "complex_documents", "highest_accuracy"],
            "speed": "slow",
            "cost": "high",
            "max_tokens": 8192
        },
        "gemini-2.5-flash": {
            "name": "gemini-2.5-flash",
            "description": "Gemini 2.5 Flash - Latest fast model with improved capabilities",
            "strengths": ["speed", "improved_accuracy", "cost_effective"],
            "speed": "fast",
            "cost": "medium",
            "max_tokens": 8192
        }
    }

    AZURE_FORM_RECOGNIZER_ENDPOINT: str = os.getenv("AZURE_FORM_RECOGNIZER_ENDPOINT", "")
    AZURE_FORM_RECOGNIZER_KEY: str = os.getenv("AZURE_FORM_RECOGNIZER_KEY", "")

    # Processing Settings
    MAX_FILE_SIZE: int = int(os.getenv("MAX_FILE_SIZE", "********"))  # 50MB for enterprise documents
    DEFAULT_OCR_ENGINE: str = os.getenv("DEFAULT_OCR_ENGINE", "gemini")  # gemini, google_document_ai, tesseract
    DEFAULT_CONFIDENCE_THRESHOLD: float = float(os.getenv("DEFAULT_CONFIDENCE_THRESHOLD", "0.85"))
    ENABLE_FALLBACKS: bool = os.getenv("ENABLE_FALLBACKS", "true").lower() == "true"
    PARALLEL_PROCESSING: bool = os.getenv("PARALLEL_PROCESSING", "true").lower() == "true"

    # LLM Intelligent Routing Settings
    LLM_ROUTING_ENABLED: bool = os.getenv("LLM_ROUTING_ENABLED", "true").lower() == "true"
    LLM_MODEL: str = os.getenv("LLM_MODEL", "gpt-4o")
    LLM_CONFIDENCE_THRESHOLD: float = float(os.getenv("LLM_CONFIDENCE_THRESHOLD", "0.8"))
    LLM_MAX_TOKENS: int = int(os.getenv("LLM_MAX_TOKENS", "4000"))
    LLM_TEMPERATURE: float = float(os.getenv("LLM_TEMPERATURE", "0.1"))  # Low temperature for consistent routing

    # GPT Post-Processing Settings
    GPT_POSTPROCESSING_ENABLED: bool = os.getenv("GPT_POSTPROCESSING_ENABLED", "true").lower() == "true"
    GPT_POSTPROCESSING_MODEL: str = os.getenv("GPT_POSTPROCESSING_MODEL", "gpt-4o")
    GPT_POSTPROCESSING_TEMPERATURE: float = float(os.getenv("GPT_POSTPROCESSING_TEMPERATURE", "0.2"))
    GPT_POSTPROCESSING_MAX_TOKENS: int = int(os.getenv("GPT_POSTPROCESSING_MAX_TOKENS", "4000"))
    GPT_VISION_MODEL: str = os.getenv("GPT_VISION_MODEL", "gpt-4o")  # For mixed content analysis

    # Google Document AI Processors - Map to actual processor IDs
    GOOGLE_PROCESSORS: Dict[str, str] = {
        "ocr": os.getenv("GOOGLE_OCR_PROCESSOR_ID", "a02d6e488186dd4"),
        "form_parser": os.getenv("GOOGLE_FORM_PARSER_PROCESSOR_ID", "a02d6e488186dd4"),  # Fallback to OCR
        "invoice": os.getenv("GOOGLE_INVOICE_PROCESSOR_ID", "a02d6e488186dd4"),  # Fallback to OCR
        "expense": os.getenv("GOOGLE_EXPENSE_PROCESSOR_ID", "a02d6e488186dd4"),  # Fallback to OCR
        "custom_extraction": os.getenv("GOOGLE_CUSTOM_PROCESSOR_ID", "a02d6e488186dd4"),  # Fallback to OCR
        "layout_parser": os.getenv("GOOGLE_LAYOUT_PROCESSOR_ID", "a02d6e488186dd4"),  # Fallback to OCR
        "contract": os.getenv("GOOGLE_CONTRACT_PROCESSOR_ID", "a02d6e488186dd4"),  # Fallback to OCR
        "bank_statement": os.getenv("GOOGLE_BANK_PROCESSOR_ID", "a02d6e488186dd4"),  # Fallback to OCR
        "paystub": os.getenv("GOOGLE_PAYSTUB_PROCESSOR_ID", "a02d6e488186dd4"),  # Fallback to OCR
        "w2": os.getenv("GOOGLE_W2_PROCESSOR_ID", "a02d6e488186dd4"),  # Fallback to OCR
        "utility": os.getenv("GOOGLE_UTILITY_PROCESSOR_ID", "a02d6e488186dd4"),  # Fallback to OCR
        "us_driver_license": os.getenv("GOOGLE_DRIVER_LICENSE_PROCESSOR_ID", "a02d6e488186dd4"),  # Fallback to OCR
        "us_passport": os.getenv("GOOGLE_PASSPORT_PROCESSOR_ID", "a02d6e488186dd4"),  # Fallback to OCR
        "id_proofing": os.getenv("GOOGLE_ID_PROOFING_PROCESSOR_ID", "a02d6e488186dd4")  # Fallback to OCR
    }

    # Processor Capabilities Matrix for intelligent routing
    PROCESSOR_CAPABILITIES: Dict[str, Dict[str, Any]] = {
        "OCR_PROCESSOR": {
            "strengths": ["general_text", "handwriting", "multi_language", "200_languages"],
            "best_for": ["simple_documents", "handwritten_notes", "multi_language"],
            "accuracy": 0.95,
            "speed": "fast",
            "cost": "low",
            "max_pages": 15
        },
        "FORM_PARSER_PROCESSOR": {
            "strengths": ["tables", "forms", "key_value_pairs", "checkboxes"],
            "best_for": ["structured_forms", "applications", "surveys"],
            "accuracy": 0.97,
            "speed": "medium",
            "cost": "medium",
            "max_pages": 15
        },
        "INVOICE_PROCESSOR": {
            "strengths": ["financial_data", "line_items", "totals", "tax_calculations"],
            "best_for": ["invoices", "bills", "financial_documents"],
            "accuracy": 0.98,
            "speed": "medium",
            "cost": "medium",
            "max_pages": 15
        },
        "EXPENSE_PROCESSOR": {
            "strengths": ["receipts", "expense_data", "merchant_info", "amounts"],
            "best_for": ["receipts", "expense_reports", "travel_expenses"],
            "accuracy": 0.97,
            "speed": "fast",
            "cost": "medium",
            "max_pages": 10
        },
        "CUSTOM_EXTRACTION_PROCESSOR": {
            "strengths": ["complex_layouts", "custom_fields", "ai_powered", "generative_ai"],
            "best_for": ["insurance_documents", "complex_forms", "specialized_docs"],
            "accuracy": 0.98,
            "speed": "slow",
            "cost": "high",
            "max_pages": 15
        },
        "LAYOUT_PARSER_PROCESSOR": {
            "strengths": ["document_structure", "content_chunking", "hierarchical_parsing"],
            "best_for": ["reports", "articles", "complex_layouts", "research_papers"],
            "accuracy": 0.96,
            "speed": "fast",
            "cost": "low",
            "max_pages": 15
        },
        "BANK_STATEMENT_PROCESSOR": {
            "strengths": ["bank_statements", "financial_records", "transaction_data", "account_info"],
            "best_for": ["bank_statements", "financial_reports", "transaction_history"],
            "accuracy": 0.97,
            "speed": "medium",
            "cost": "medium",
            "max_pages": 15
        }
    }

    # Debug Settings
    DEBUG_MODE: bool = os.getenv("DEBUG_MODE", "false").lower() == "true"
    SAVE_INTERMEDIATE_IMAGES: bool = os.getenv("SAVE_INTERMEDIATE_IMAGES", "false").lower() == "true"
    COLLECT_ALL_OUTPUTS: bool = os.getenv("COLLECT_ALL_OUTPUTS", "false").lower() == "true"
    DETAILED_TIMING: bool = os.getenv("DETAILED_TIMING", "false").lower() == "true"

    # Performance Settings
    VISION_COST_THRESHOLD: float = float(os.getenv("VISION_COST_THRESHOLD", "0.005"))
    MAX_WORKERS: int = int(os.getenv("MAX_WORKERS", "8"))  # High performance processing
    ENABLE_CACHE: bool = os.getenv("ENABLE_CACHE", "true").lower() == "true"
    CACHE_TTL: int = int(os.getenv("CACHE_TTL", "3600"))
    BATCH_SIZE: int = int(os.getenv("BATCH_SIZE", "10"))
    REQUEST_TIMEOUT: int = int(os.getenv("REQUEST_TIMEOUT", "300"))  # 5 minutes

    # Comprehensive File Format Support
    SUPPORTED_FORMATS: List[str] = [
        # Images
        "pdf", "png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp", "svg", "ico",
        # Microsoft Office
        "docx", "doc", "xlsx", "xls", "pptx", "ppt", "pptm",
        # Email and Web
        "msg", "eml", "html", "htm", "xml", "mhtml",
        # Text and Data
        "csv", "txt", "rtf", "json", "yaml", "yml",
        # Open Office
        "odt", "ods", "odp", "odg",
        # Archives
        "zip", "rar", "7z", "tar", "gz", "bz2",
        # Business Intelligence
        "pbix", "pbit",
        # Other formats
        "eps", "ps", "ai"
    ]

    class Config:
        env_file = ".env"
        extra = "allow"  # Allow extra fields from .env

# Global settings instance
settings = Settings()

# Legacy compatibility
MAX_FILE_SIZE = settings.MAX_FILE_SIZE
SUPPORTED_FORMATS = settings.SUPPORTED_FORMATS
DEBUG_MODE = settings.DEBUG_MODE
