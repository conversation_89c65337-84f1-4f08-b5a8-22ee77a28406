"""
Intelligent Prompt Processor for Zurich OCR Engine
Combines template engine, document classification, and LLM processing
"""

import json
import logging
import asyncio
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict

from .template_engine import TemplateEngine
from .document_classifier import DocumentClassifier, ClassificationResult
from .config import settings

logger = logging.getLogger(__name__)

@dataclass
class ProcessingResult:
    """Result of intelligent prompt processing"""
    success: bool
    document_classification: ClassificationResult
    extracted_data: Dict[str, Any]
    processing_time: float
    confidence_scores: Dict[str, float]
    error_message: Optional[str] = None
    raw_output: Optional[str] = None

class IntelligentPromptProcessor:
    """
    Advanced prompt processor that combines classification, templating, and LLM processing
    Optimized for speed and accuracy across all Zurich document types
    """
    
    def __init__(self):
        """Initialize the intelligent prompt processor"""
        self.template_engine = TemplateEngine()
        self.document_classifier = DocumentClassifier()
        self.processing_cache = {}
        logger.info("Intelligent prompt processor initialized")
    
    async def process_document(self, 
                             content: str,
                             filename: str = "",
                             ocr_confidence: float = 0.8,
                             processing_mode: str = "balanced",
                             llm_client = None) -> ProcessingResult:
        """
        Process document with intelligent prompt selection and optimization
        
        Args:
            content: Document text content
            filename: Original filename
            ocr_confidence: OCR quality score
            processing_mode: Processing mode (fast, balanced, detailed)
            llm_client: LLM client for processing
            
        Returns:
            ProcessingResult with extracted data and metadata
        """
        
        import time
        start_time = time.time()
        
        try:
            # Step 1: Classify document
            classification = self.document_classifier.classify_document(
                content=content,
                filename=filename
            )
            
            logger.info(f"Document classified as {classification.document_type} "
                       f"({classification.category}) with {classification.confidence:.2f} confidence")
            
            # Step 2: Select optimal processing strategy
            processing_strategy = self._select_processing_strategy(
                classification=classification,
                ocr_confidence=ocr_confidence,
                processing_mode=processing_mode
            )
            
            # Step 3: Generate intelligent prompt
            prompt = self._generate_intelligent_prompt(
                content=content,
                classification=classification,
                strategy=processing_strategy
            )
            
            # Step 4: Process with LLM
            if llm_client:
                llm_response = await self._process_with_llm(
                    prompt=prompt,
                    strategy=processing_strategy,
                    llm_client=llm_client
                )
            else:
                # Fallback for testing
                llm_response = self._create_fallback_response(content, classification)
            
            # Step 5: Parse and validate output
            extracted_data = self.template_engine.validate_template_output(llm_response)
            
            # Step 6: Calculate confidence scores
            confidence_scores = self._calculate_confidence_scores(
                classification=classification,
                ocr_confidence=ocr_confidence,
                extracted_data=extracted_data
            )
            
            processing_time = time.time() - start_time
            
            return ProcessingResult(
                success=True,
                document_classification=classification,
                extracted_data=extracted_data,
                processing_time=processing_time,
                confidence_scores=confidence_scores,
                raw_output=llm_response
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error in intelligent prompt processing: {e}")
            
            return ProcessingResult(
                success=False,
                document_classification=classification if 'classification' in locals() else None,
                extracted_data={},
                processing_time=processing_time,
                confidence_scores={},
                error_message=str(e)
            )
    
    def _select_processing_strategy(self, 
                                  classification: ClassificationResult,
                                  ocr_confidence: float,
                                  processing_mode: str) -> Dict[str, Any]:
        """Select optimal processing strategy based on document characteristics"""
        
        # Base strategy
        strategy = {
            "template": "balanced_intelligent_prompt.tmpl",
            "model": "gpt-4o-mini",
            "temperature": 0.1,
            "max_tokens": 2000,
            "timeout": 30
        }
        
        # Adjust based on processing mode
        if processing_mode == "fast":
            strategy.update({
                "model": "gpt-3.5-turbo",
                "max_tokens": 1000,
                "timeout": 15,
                "template": "fast_extraction_prompt.tmpl"
            })
        elif processing_mode == "detailed":
            strategy.update({
                "model": "gpt-4o",
                "max_tokens": 4000,
                "timeout": 60,
                "template": "detailed_analysis_prompt.tmpl"
            })
        
        # Adjust based on document category
        if classification.category == "CLAIMS":
            strategy.update({
                "model": "gpt-4o",  # Use best model for claims
                "temperature": 0.05,  # Very low temperature for accuracy
                "max_tokens": 3000
            })
        elif classification.category == "FINANCE":
            strategy.update({
                "model": "gpt-4o",  # Use best model for financial data
                "temperature": 0.05,
                "max_tokens": 3000
            })
        
        # Adjust based on OCR quality
        if ocr_confidence < 0.6:
            strategy.update({
                "model": "gpt-4o",  # Use best model for poor quality
                "temperature": 0.1,
                "max_tokens": 4000,
                "timeout": 60
            })
        
        # Adjust based on document confidence
        if classification.confidence < 0.7:
            strategy.update({
                "model": "gpt-4o",  # Use best model for uncertain classification
                "temperature": 0.1
            })
        
        return strategy
    
    def _generate_intelligent_prompt(self, 
                                   content: str,
                                   classification: ClassificationResult,
                                   strategy: Dict[str, Any]) -> str:
        """Generate intelligent prompt using template engine"""
        
        # Optimize content length for token limits
        optimized_content = self._optimize_content_for_tokens(
            content=content,
            max_tokens=strategy.get("max_tokens", 2000),
            document_type=classification.document_type
        )
        
        # Render prompt with intelligent context
        prompt = self.template_engine.render_prompt(
            template_name=strategy["template"],
            document_content=optimized_content,
            document_type=classification.document_type,
            region=classification.region,
            quality_score=classification.confidence,
            processing_mode=strategy.get("processing_mode", "balanced")
        )
        
        return prompt
    
    def _optimize_content_for_tokens(self, 
                                   content: str,
                                   max_tokens: int,
                                   document_type: str) -> str:
        """Optimize content length while preserving important information"""
        
        # Rough token estimation (1 token ≈ 4 characters)
        estimated_tokens = len(content) // 4
        
        if estimated_tokens <= max_tokens * 0.7:  # Leave 30% for prompt and response
            return content
        
        # Smart truncation based on document type
        target_length = int(max_tokens * 0.7 * 4)  # Convert back to characters
        
        if document_type in ["travel_claim", "motor_claim", "disability_claim"]:
            # For claims, keep beginning and end (often contains key info)
            keep_start = target_length // 2
            keep_end = target_length // 2
            return content[:keep_start] + "\n...[content truncated]...\n" + content[-keep_end:]
        
        elif document_type in ["financial_report", "contract"]:
            # For financial docs, keep beginning (summary) and structured data
            return content[:target_length] + "\n...[content truncated]..."
        
        else:
            # Default: keep beginning
            return content[:target_length] + "\n...[content truncated]..."
    
    async def _process_with_llm(self, 
                              prompt: str,
                              strategy: Dict[str, Any],
                              llm_client) -> str:
        """Process prompt with LLM using optimal strategy"""
        
        try:
            # Use the existing LLM client (GPT post-processing)
            if hasattr(llm_client, 'process_content'):
                response = await llm_client.process_content(
                    content=prompt,
                    model=strategy.get("model", "gpt-4o-mini"),
                    temperature=strategy.get("temperature", 0.1),
                    max_tokens=strategy.get("max_tokens", 2000)
                )
                return response
            else:
                # Fallback for different client interfaces
                return await llm_client.generate_response(prompt)
                
        except Exception as e:
            logger.error(f"LLM processing failed: {e}")
            raise
    
    def _create_fallback_response(self, 
                                content: str,
                                classification: ClassificationResult) -> str:
        """Create fallback response when LLM is not available"""
        
        fallback_data = {
            "document_classification": {
                "primary_category": classification.category,
                "document_type": classification.document_type,
                "geographic_region": classification.region,
                "confidence": classification.confidence
            },
            "extracted_text": content,
            "key_entities": {
                "financial_amounts": [],
                "dates": [],
                "people": [],
                "organizations": [],
                "reference_numbers": [],
                "locations": []
            },
            "domain_specific_data": {},
            "processing_intelligence": {
                "suggested_title": f"{classification.document_type.replace('_', ' ').title()} Document",
                "suggested_tags": [classification.category.lower(), classification.document_type],
                "suggested_correspondent": "Unknown",
                "priority_level": "medium",
                "next_steps": ["Review document", "Extract key information"]
            },
            "quality_metrics": {
                "overall_confidence": classification.confidence,
                "extraction_confidence": 0.5,
                "classification_confidence": classification.confidence,
                "completeness_score": 0.5,
                "processing_notes": ["Fallback processing - LLM not available"]
            }
        }
        
        return json.dumps(fallback_data, indent=2)
    
    def _calculate_confidence_scores(self, 
                                   classification: ClassificationResult,
                                   ocr_confidence: float,
                                   extracted_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate comprehensive confidence scores"""
        
        # Base confidence from classification
        classification_confidence = classification.confidence
        
        # OCR quality confidence
        ocr_quality_confidence = ocr_confidence
        
        # Extraction completeness confidence
        extraction_confidence = 0.8  # Default
        if "key_entities" in extracted_data:
            entities = extracted_data["key_entities"]
            entity_count = sum(len(v) if isinstance(v, list) else 1 for v in entities.values())
            extraction_confidence = min(entity_count / 10.0, 1.0)  # Normalize
        
        # Overall confidence (weighted average)
        overall_confidence = (
            classification_confidence * 0.3 +
            ocr_quality_confidence * 0.4 +
            extraction_confidence * 0.3
        )
        
        return {
            "overall": round(overall_confidence, 3),
            "classification": round(classification_confidence, 3),
            "ocr_quality": round(ocr_quality_confidence, 3),
            "extraction": round(extraction_confidence, 3)
        }
