"""
Template Engine for Dynamic Prompt Generation
Handles template loading, rendering, and context building for intelligent document processing
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path
from jinja2 import Environment, FileSystemLoader, Template
from datetime import datetime

logger = logging.getLogger(__name__)

class TemplateEngine:
    """
    Advanced template engine for dynamic prompt generation
    Supports Jinja2 templates with intelligent context building
    """
    
    def __init__(self, templates_dir: str = "prompts"):
        """Initialize template engine with templates directory"""
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(exist_ok=True)
        
        # Initialize Jinja2 environment
        self.env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # Add custom filters
        self.env.filters['join'] = lambda x, sep=', ': sep.join(x) if x else ''
        self.env.filters['truncate'] = lambda x, length=500: x[:length] + '...' if len(x) > length else x
        
        logger.info(f"Template engine initialized with directory: {self.templates_dir}")
    
    def get_template(self, template_name: str) -> Template:
        """Load and return a template by name"""
        try:
            return self.env.get_template(template_name)
        except Exception as e:
            logger.error(f"Failed to load template {template_name}: {e}")
            raise
    
    def build_context(self, 
                     document_content: str,
                     document_type: str,
                     region: str = "Global",
                     quality_score: float = 0.8,
                     processing_mode: str = "standard",
                     **kwargs) -> Dict[str, Any]:
        """
        Build intelligent context for template rendering
        
        Args:
            document_content: The document text content
            document_type: Type of document (travel_claim, motor_claim, etc.)
            region: Geographic region (Canada, UK, Germany, etc.)
            quality_score: OCR quality score (0.0 to 1.0)
            processing_mode: Processing mode (standard, fast, detailed)
            **kwargs: Additional context variables
        
        Returns:
            Dictionary containing template context
        """
        
        # Base context
        context = {
            "document_type": document_type,
            "region": region,
            "quality_score": quality_score,
            "processing_mode": processing_mode,
            "content": document_content,
            "content_length": len(document_content),
            "timestamp": datetime.now().isoformat(),
            "language": kwargs.get("language", "English")
        }
        
        # Add document type specific context
        context.update(self._get_document_type_context(document_type, region))
        
        # Add quality-based context
        context.update(self._get_quality_context(quality_score))
        
        # Add region-specific context
        context.update(self._get_region_context(region))
        
        # Add any additional kwargs
        context.update(kwargs)
        
        return context
    
    def _get_document_type_context(self, document_type: str, region: str) -> Dict[str, Any]:
        """Get context specific to document type"""
        
        # Document type mappings
        type_contexts = {
            "travel_claim": {
                "category": "CLAIMS",
                "expected_entities": ["receipt_amount", "vendor", "flight_number", "policy_number"],
                "key_fields": ["claimant", "incident_date", "claim_amount", "policy_coverage"],
                "validation_patterns": ["POL\\d+", "CLM\\d+", "\\$[\\d,]+\\.\\d{2}"]
            },
            "motor_claim": {
                "category": "CLAIMS", 
                "expected_entities": ["vehicle_registration", "accident_date", "damage_amount", "policy_number"],
                "key_fields": ["incident_location", "parties_involved", "liability_assessment"],
                "validation_patterns": ["POL\\d+", "CLM\\d+", "[A-Z]{2}\\d{2}[A-Z]{3}"]
            },
            "disability_claim": {
                "category": "CLAIMS",
                "expected_entities": ["medical_diagnosis", "disability_date", "benefit_amount"],
                "key_fields": ["claimant", "medical_provider", "work_capacity"],
                "validation_patterns": ["POL\\d+", "CLM\\d+", "ICD-\\d+"]
            },
            "underwriting_application": {
                "category": "UNDERWRITING",
                "expected_entities": ["business_name", "revenue", "employee_count", "industry"],
                "key_fields": ["risk_assessment", "coverage_requested", "financial_metrics"],
                "validation_patterns": ["APP\\d+", "\\$[\\d,]+", "\\d+%"]
            },
            "bond_surety": {
                "category": "UNDERWRITING",
                "expected_entities": ["bond_amount", "project_duration", "contractor", "beneficiary"],
                "key_fields": ["financial_capacity", "project_details", "risk_factors"],
                "validation_patterns": ["BOND\\d+", "€[\\d,]+", "\\d{4}-\\d{2}-\\d{2}"]
            },
            "seo_report": {
                "category": "MARKETING",
                "expected_entities": ["website_url", "keyword_rankings", "traffic_metrics"],
                "key_fields": ["seo_score", "recommendations", "performance_trends"],
                "validation_patterns": ["https?://[\\w.-]+", "\\d+%", "\\d+k?"]
            },
            "financial_report": {
                "category": "FINANCE",
                "expected_entities": ["revenue", "expenses", "profit_margin", "report_period"],
                "key_fields": ["financial_ratios", "ifrs17_data", "variance_analysis"],
                "validation_patterns": ["\\$[\\d,]+", "\\d+\\.\\d+%", "Q[1-4]\\s\\d{4}"]
            },
            "contract": {
                "category": "FINANCE",
                "expected_entities": ["contract_value", "duration", "parties", "terms"],
                "key_fields": ["obligations", "penalties", "payment_schedule"],
                "validation_patterns": ["CNT\\d+", "\\$[\\d,]+", "\\d{4}-\\d{2}-\\d{2}"]
            }
        }
        
        return type_contexts.get(document_type, {
            "category": "OTHER",
            "expected_entities": ["general_entities"],
            "key_fields": ["main_content"],
            "validation_patterns": ["\\d+", "\\$[\\d,]+"]
        })
    
    def _get_quality_context(self, quality_score: float) -> Dict[str, Any]:
        """Get context based on OCR quality score"""
        
        if quality_score < 0.6:
            return {
                "quality_level": "poor",
                "error_correction_needed": True,
                "confidence_threshold": 0.7,
                "processing_strategy": "aggressive_correction"
            }
        elif quality_score < 0.8:
            return {
                "quality_level": "fair", 
                "error_correction_needed": True,
                "confidence_threshold": 0.8,
                "processing_strategy": "moderate_correction"
            }
        else:
            return {
                "quality_level": "good",
                "error_correction_needed": False,
                "confidence_threshold": 0.9,
                "processing_strategy": "preserve_original"
            }
    
    def _get_region_context(self, region: str) -> Dict[str, Any]:
        """Get context specific to geographic region"""
        
        region_contexts = {
            "Canada": {
                "currency": "CAD",
                "date_format": "YYYY-MM-DD",
                "regulations": ["PIPEDA", "Provincial Insurance Acts"],
                "languages": ["English", "French"]
            },
            "UK": {
                "currency": "GBP", 
                "date_format": "DD/MM/YYYY",
                "regulations": ["GDPR", "FCA Rules", "Motor Insurance Act"],
                "languages": ["English"]
            },
            "Germany": {
                "currency": "EUR",
                "date_format": "DD.MM.YYYY", 
                "regulations": ["GDPR", "VAG", "BGB"],
                "languages": ["German", "English"]
            },
            "Switzerland": {
                "currency": "CHF",
                "date_format": "DD.MM.YYYY",
                "regulations": ["FINMA", "DSG", "VVG"],
                "languages": ["German", "French", "Italian", "English"]
            },
            "Australia": {
                "currency": "AUD",
                "date_format": "DD/MM/YYYY",
                "regulations": ["APRA", "Privacy Act", "Insurance Contracts Act"],
                "languages": ["English"]
            },
            "Ecuador": {
                "currency": "USD",
                "date_format": "DD/MM/YYYY",
                "regulations": ["Superintendencia de Seguros"],
                "languages": ["Spanish", "English"]
            }
        }
        
        return region_contexts.get(region, {
            "currency": "USD",
            "date_format": "YYYY-MM-DD",
            "regulations": ["General"],
            "languages": ["English"]
        })
    
    def render_prompt(self, 
                     template_name: str,
                     document_content: str,
                     document_type: str,
                     **kwargs) -> str:
        """
        Render a prompt template with intelligent context
        
        Args:
            template_name: Name of template file (e.g., 'balanced_intelligent_prompt.tmpl')
            document_content: The document text content
            document_type: Type of document
            **kwargs: Additional context variables
        
        Returns:
            Rendered prompt string
        """
        
        try:
            # Build context
            context = self.build_context(
                document_content=document_content,
                document_type=document_type,
                **kwargs
            )
            
            # Load and render template
            template = self.get_template(template_name)
            rendered_prompt = template.render(**context)
            
            logger.info(f"Successfully rendered template {template_name} for {document_type}")
            return rendered_prompt
            
        except Exception as e:
            logger.error(f"Failed to render template {template_name}: {e}")
            raise
    
    def validate_template_output(self, output: str) -> Dict[str, Any]:
        """
        Validate and parse template output
        
        Args:
            output: Raw output from LLM
            
        Returns:
            Parsed and validated output dictionary
        """
        
        try:
            # Try to parse as JSON
            if output.strip().startswith('{'):
                return json.loads(output)
            else:
                # Extract JSON from text if wrapped in other content
                start_idx = output.find('{')
                end_idx = output.rfind('}') + 1
                if start_idx != -1 and end_idx != 0:
                    json_str = output[start_idx:end_idx]
                    return json.loads(json_str)
                else:
                    raise ValueError("No valid JSON found in output")
                    
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON output: {e}")
            # Return fallback structure
            return {
                "extracted_text": output,
                "error": "Failed to parse structured output",
                "raw_output": output
            }
    
    def get_available_templates(self) -> list:
        """Get list of available template files"""
        
        template_files = []
        for file_path in self.templates_dir.glob("*.tmpl"):
            template_files.append(file_path.name)
        
        return sorted(template_files)
