"""
Advanced Document Classifier for Zurich OCR Engine
Intelligently detects document types and regions for optimal processing
"""

import re
import logging
from typing import Dict, Tu<PERSON>, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ClassificationResult:
    """Result of document classification"""
    document_type: str
    category: str
    region: str
    confidence: float
    detected_patterns: List[str]
    suggested_processing: str

class DocumentClassifier:
    """
    Intelligent document classifier for all Zurich document types
    Uses pattern matching, keyword analysis, and content structure
    """
    
    def __init__(self):
        """Initialize classifier with patterns and rules"""
        self.classification_rules = self._build_classification_rules()
        self.region_patterns = self._build_region_patterns()
        logger.info("Document classifier initialized")
    
    def classify_document(self, 
                         content: str, 
                         filename: str = "",
                         metadata: Dict = None) -> ClassificationResult:
        """
        Classify document based on content, filename, and metadata
        
        Args:
            content: Document text content
            filename: Original filename
            metadata: Additional metadata
            
        Returns:
            ClassificationResult with detected type, category, and region
        """
        
        content_lower = content.lower()
        filename_lower = filename.lower()
        
        # Detect region first
        region = self._detect_region(content, filename)
        
        # Score each document type
        type_scores = {}
        detected_patterns = []
        
        for doc_type, rules in self.classification_rules.items():
            score = 0
            type_patterns = []
            
            # Check content patterns
            for pattern in rules.get("content_patterns", []):
                matches = len(re.findall(pattern, content_lower))
                if matches > 0:
                    score += matches * rules.get("pattern_weight", 1)
                    type_patterns.append(f"content:{pattern}")
            
            # Check filename patterns
            for pattern in rules.get("filename_patterns", []):
                if re.search(pattern, filename_lower):
                    score += rules.get("filename_weight", 2)
                    type_patterns.append(f"filename:{pattern}")
            
            # Check required keywords
            required_keywords = rules.get("required_keywords", [])
            found_keywords = sum(1 for kw in required_keywords if kw in content_lower)
            if required_keywords:
                keyword_ratio = found_keywords / len(required_keywords)
                score += keyword_ratio * rules.get("keyword_weight", 3)
                if found_keywords > 0:
                    type_patterns.append(f"keywords:{found_keywords}/{len(required_keywords)}")
            
            # Check structural indicators
            for indicator in rules.get("structure_indicators", []):
                if indicator in content_lower:
                    score += rules.get("structure_weight", 1)
                    type_patterns.append(f"structure:{indicator}")
            
            if score > 0:
                type_scores[doc_type] = score
                detected_patterns.extend(type_patterns)
        
        # Determine best match
        if type_scores:
            best_type = max(type_scores.keys(), key=lambda k: type_scores[k])
            confidence = min(type_scores[best_type] / 10.0, 1.0)  # Normalize to 0-1
            category = self.classification_rules[best_type]["category"]
            processing = self.classification_rules[best_type].get("processing_mode", "standard")
        else:
            best_type = "general_document"
            category = "OTHER"
            confidence = 0.5
            processing = "standard"
        
        return ClassificationResult(
            document_type=best_type,
            category=category,
            region=region,
            confidence=confidence,
            detected_patterns=detected_patterns,
            suggested_processing=processing
        )
    
    def _detect_region(self, content: str, filename: str) -> str:
        """Detect geographic region from content and filename"""
        
        content_lower = content.lower()
        filename_lower = filename.lower()
        
        region_scores = {}
        
        for region, patterns in self.region_patterns.items():
            score = 0
            
            # Check content patterns
            for pattern in patterns.get("content_indicators", []):
                matches = len(re.findall(pattern, content_lower))
                score += matches
            
            # Check filename patterns
            for pattern in patterns.get("filename_indicators", []):
                if re.search(pattern, filename_lower):
                    score += 2
            
            # Check currency patterns
            for currency in patterns.get("currencies", []):
                # Escape special regex characters in currency symbols
                escaped_currency = re.escape(currency)
                currency_pattern = f"{escaped_currency}[\\d,]+\\.?\\d*"
                matches = len(re.findall(currency_pattern, content))
                score += matches * 2
            
            if score > 0:
                region_scores[region] = score
        
        if region_scores:
            return max(region_scores.keys(), key=lambda k: region_scores[k])
        else:
            return "Global"
    
    def _build_classification_rules(self) -> Dict:
        """Build comprehensive classification rules for all document types"""
        
        return {
            "travel_claim": {
                "category": "CLAIMS",
                "content_patterns": [
                    r"travel\s+claim", r"baggage\s+report", r"flight\s+delay",
                    r"trip\s+cancellation", r"medical\s+emergency", r"receipt",
                    r"airline\s+ticket", r"hotel\s+booking", r"travel\s+insurance"
                ],
                "filename_patterns": [
                    r"travel", r"receipt", r"baggage", r"flight", r"trip"
                ],
                "required_keywords": [
                    "claim", "travel", "policy"
                ],
                "structure_indicators": [
                    "departure date", "return date", "destination", "claimant",
                    "policy number", "claim amount"
                ],
                "processing_mode": "detailed",
                "pattern_weight": 2,
                "filename_weight": 3,
                "keyword_weight": 4,
                "structure_weight": 2
            },
            
            "motor_claim": {
                "category": "CLAIMS", 
                "content_patterns": [
                    r"motor\s+claim", r"vehicle\s+accident", r"car\s+insurance",
                    r"collision", r"liability", r"third\s+party", r"vehicle\s+damage",
                    r"registration\s+number", r"driving\s+licence"
                ],
                "filename_patterns": [
                    r"motor", r"vehicle", r"accident", r"collision", r"auto"
                ],
                "required_keywords": [
                    "vehicle", "accident", "claim"
                ],
                "structure_indicators": [
                    "accident date", "vehicle registration", "driver", "damage",
                    "policy number", "incident location"
                ],
                "processing_mode": "detailed",
                "pattern_weight": 2,
                "filename_weight": 3,
                "keyword_weight": 4,
                "structure_weight": 2
            },
            
            "disability_claim": {
                "category": "CLAIMS",
                "content_patterns": [
                    r"disability\s+claim", r"medical\s+report", r"diagnosis",
                    r"work\s+capacity", r"functional\s+assessment", r"physician",
                    r"medical\s+certificate", r"disability\s+benefit"
                ],
                "filename_patterns": [
                    r"disability", r"medical", r"diagnosis", r"physician"
                ],
                "required_keywords": [
                    "disability", "medical", "claim"
                ],
                "structure_indicators": [
                    "diagnosis", "medical provider", "disability date",
                    "work restrictions", "benefit amount"
                ],
                "processing_mode": "detailed",
                "pattern_weight": 2,
                "filename_weight": 3,
                "keyword_weight": 4,
                "structure_weight": 2
            },
            
            "underwriting_application": {
                "category": "UNDERWRITING",
                "content_patterns": [
                    r"application", r"underwriting", r"risk\s+assessment",
                    r"business\s+profile", r"financial\s+statement", r"revenue",
                    r"employee\s+count", r"industry\s+classification"
                ],
                "filename_patterns": [
                    r"application", r"underwriting", r"risk", r"assessment"
                ],
                "required_keywords": [
                    "application", "business", "coverage"
                ],
                "structure_indicators": [
                    "business name", "industry", "revenue", "employees",
                    "coverage requested", "risk factors"
                ],
                "processing_mode": "standard",
                "pattern_weight": 2,
                "filename_weight": 3,
                "keyword_weight": 3,
                "structure_weight": 2
            },
            
            "bond_surety": {
                "category": "UNDERWRITING",
                "content_patterns": [
                    r"bond", r"surety", r"guarantee", r"performance\s+bond",
                    r"payment\s+bond", r"bid\s+bond", r"contractor",
                    r"principal", r"obligee", r"project"
                ],
                "filename_patterns": [
                    r"bond", r"surety", r"guarantee", r"performance"
                ],
                "required_keywords": [
                    "bond", "contractor", "project"
                ],
                "structure_indicators": [
                    "bond amount", "project duration", "contractor name",
                    "beneficiary", "bond type"
                ],
                "processing_mode": "standard",
                "pattern_weight": 2,
                "filename_weight": 3,
                "keyword_weight": 3,
                "structure_weight": 2
            },
            
            "seo_report": {
                "category": "MARKETING",
                "content_patterns": [
                    r"seo", r"search\s+engine", r"keyword", r"ranking",
                    r"organic\s+traffic", r"backlink", r"page\s+speed",
                    r"meta\s+description", r"google\s+analytics"
                ],
                "filename_patterns": [
                    r"seo", r"search", r"ranking", r"analytics", r"traffic"
                ],
                "required_keywords": [
                    "seo", "website", "ranking"
                ],
                "structure_indicators": [
                    "keyword ranking", "organic traffic", "page views",
                    "bounce rate", "conversion rate"
                ],
                "processing_mode": "fast",
                "pattern_weight": 2,
                "filename_weight": 3,
                "keyword_weight": 3,
                "structure_weight": 1
            },
            
            "financial_report": {
                "category": "FINANCE",
                "content_patterns": [
                    r"financial\s+report", r"ifrs17", r"balance\s+sheet",
                    r"profit\s+and\s+loss", r"cash\s+flow", r"revenue",
                    r"expenses", r"assets", r"liabilities", r"equity"
                ],
                "filename_patterns": [
                    r"financial", r"ifrs", r"balance", r"profit", r"revenue"
                ],
                "required_keywords": [
                    "financial", "report", "revenue"
                ],
                "structure_indicators": [
                    "total revenue", "net profit", "total assets",
                    "report period", "financial ratios"
                ],
                "processing_mode": "detailed",
                "pattern_weight": 2,
                "filename_weight": 3,
                "keyword_weight": 4,
                "structure_weight": 2
            },
            
            "contract": {
                "category": "FINANCE",
                "content_patterns": [
                    r"contract", r"agreement", r"terms\s+and\s+conditions",
                    r"procurement", r"vendor", r"supplier", r"service\s+level",
                    r"payment\s+terms", r"deliverables"
                ],
                "filename_patterns": [
                    r"contract", r"agreement", r"procurement", r"vendor"
                ],
                "required_keywords": [
                    "contract", "agreement", "terms"
                ],
                "structure_indicators": [
                    "contract value", "contract duration", "parties",
                    "payment terms", "deliverables"
                ],
                "processing_mode": "standard",
                "pattern_weight": 2,
                "filename_weight": 3,
                "keyword_weight": 3,
                "structure_weight": 2
            }
        }
    
    def _build_region_patterns(self) -> Dict:
        """Build region detection patterns"""
        
        return {
            "Canada": {
                "content_indicators": [
                    r"canada", r"canadian", r"ontario", r"quebec", r"british columbia",
                    r"alberta", r"manitoba", r"saskatchewan", r"nova scotia",
                    r"postal code", r"[a-z]\d[a-z]\s*\d[a-z]\d"
                ],
                "filename_indicators": [
                    r"canada", r"canadian", r"ca", r"toronto", r"vancouver"
                ],
                "currencies": ["CAD", "C$"]
            },
            
            "UK": {
                "content_indicators": [
                    r"united kingdom", r"england", r"scotland", r"wales",
                    r"northern ireland", r"london", r"manchester", r"birmingham",
                    r"postcode", r"[a-z]{1,2}\d{1,2}[a-z]?\s*\d[a-z]{2}"
                ],
                "filename_indicators": [
                    r"uk", r"england", r"london", r"british"
                ],
                "currencies": ["GBP", "£"]
            },
            
            "Germany": {
                "content_indicators": [
                    r"germany", r"deutschland", r"german", r"berlin", r"munich",
                    r"hamburg", r"cologne", r"frankfurt", r"plz", r"\d{5}"
                ],
                "filename_indicators": [
                    r"germany", r"german", r"de", r"berlin", r"munich"
                ],
                "currencies": ["EUR", "€"]
            },
            
            "Switzerland": {
                "content_indicators": [
                    r"switzerland", r"swiss", r"zurich", r"geneva", r"basel",
                    r"bern", r"lausanne", r"schweiz", r"suisse"
                ],
                "filename_indicators": [
                    r"switzerland", r"swiss", r"ch", r"zurich", r"geneva"
                ],
                "currencies": ["CHF", "Fr."]
            },
            
            "Australia": {
                "content_indicators": [
                    r"australia", r"australian", r"sydney", r"melbourne",
                    r"brisbane", r"perth", r"adelaide", r"postcode",
                    r"\d{4}"
                ],
                "filename_indicators": [
                    r"australia", r"australian", r"au", r"sydney", r"melbourne"
                ],
                "currencies": ["AUD", "A$"]
            },
            
            "Ecuador": {
                "content_indicators": [
                    r"ecuador", r"ecuadorian", r"quito", r"guayaquil",
                    r"cuenca", r"ambato", r"machala"
                ],
                "filename_indicators": [
                    r"ecuador", r"ecuadorian", r"ec", r"quito"
                ],
                "currencies": ["USD", "$"]
            }
        }
