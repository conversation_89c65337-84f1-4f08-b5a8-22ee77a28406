"""
Gemini Vision OCR Engine - Advanced OCR with Google's Gemini Vision API

This module provides OCR capabilities using Google's Gemini Vision models,
specifically optimized for accurate text extraction with minimal hallucination.
Includes automatic fallback to Tesseract for unsupported file formats.
"""

import logging
import time
import base64
import mimetypes
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import asyncio

try:
    import vertexai
    from vertexai.preview.generative_models import GenerativeModel, Part, Image
    from google.cloud import aiplatform
    VERTEX_AI_AVAILABLE = True
except ImportError:
    VERTEX_AI_AVAILABLE = False
    vertexai = None
    aiplatform = None
    GenerativeModel = None
    Part = None
    Image = None

from .config import settings

logger = logging.getLogger(__name__)

@dataclass
class GeminiVisionResult:
    """Result from Gemini Vision OCR processing"""
    success: bool
    text: str
    confidence: float
    processing_time: float
    model_used: str
    model_info: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    fallback_used: bool = False
    file_supported: bool = True

class GeminiVisionEngine:
    """
    Gemini Vision OCR Engine with Tesseract fallback

    Features:
    - Uses Vertex AI with service account authentication
    - Supports multiple Gemini models (1.5 Pro/Flash, 2.5 Pro/Flash)
    - Anti-hallucination prompts for reliable text extraction
    - Automatic fallback to Tesseract for unsupported formats
    - Support for multiple image formats
    """

    def __init__(self):
        self.client = None
        self.models = {}  # Cache for different model instances
        self.credentials_file = None  # Store path to temporary credentials file
        self.supported_formats = {
            "image/png", "image/jpeg", "image/jpg", "image/webp",
            "image/gif", "image/bmp", "image/tiff", "image/ico"
        }

        # Initialize Vertex AI with service account
        if self._init_vertex_ai():
            logger.info("Gemini Vision initialized via Vertex AI with service account")
        else:
            logger.warning("Gemini Vision not available - check service account configuration")

    def _init_vertex_ai(self) -> bool:
        """Initialize Vertex AI with service account credentials"""
        if not VERTEX_AI_AVAILABLE:
            logger.warning("Vertex AI library not available. Install with: pip install google-cloud-aiplatform")
            return False

        if not settings.GOOGLE_PROJECT_ID or not settings.GOOGLE_CREDENTIALS_JSON:
            logger.warning("Vertex AI not configured - missing GOOGLE_PROJECT_ID or GOOGLE_CREDENTIALS_JSON")
            return False

        try:
            import json
            import tempfile
            import os

            # Create temporary credentials file that persists for the session
            credentials_data = json.loads(settings.GOOGLE_CREDENTIALS_JSON)
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(credentials_data, f)
                self.credentials_file = f.name

            # Initialize Vertex AI
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = self.credentials_file
            vertexai.init(
                project=settings.GOOGLE_PROJECT_ID,
                location=settings.GOOGLE_LOCATION
            )

            self.client = aiplatform

            # Test connection by trying to list models (this validates credentials)
            try:
                # This is a simple test to validate the connection
                aiplatform.init(
                    project=settings.GOOGLE_PROJECT_ID,
                    location=settings.GOOGLE_LOCATION
                )
                logger.info(f"Vertex AI initialized successfully")
                logger.info(f"Project: {settings.GOOGLE_PROJECT_ID}, Location: {settings.GOOGLE_LOCATION}")
                logger.info(f"Available Gemini models: {list(settings.GEMINI_MODELS.keys())}")

                # Store a simple flag to indicate we're ready
                self.models["initialized"] = True

            except Exception as test_e:
                logger.error(f"Vertex AI connection test failed: {str(test_e)}")
                return False

            return True

        except Exception as e:
            logger.error(f"Failed to initialize Vertex AI: {str(e)}")
            logger.error("Please check your service account credentials and project configuration")
            return False
    
    def is_available(self) -> bool:
        """Check if Gemini Vision is available"""
        return self.client is not None and len(self.models) > 0

    def get_model(self, model_name: str = None):
        """
        Get or validate a Gemini model name for Vertex AI

        Args:
            model_name: Specific model name (e.g., "gemini-1.5-pro", "gemini-2.5-flash")
                       If None, uses the default model from settings

        Returns:
            Model name string if valid, None if not available
        """
        if not self.client or "initialized" not in self.models:
            return None

        if model_name is None:
            model_name = settings.GEMINI_MODEL

        # Validate model name
        if model_name not in settings.GEMINI_MODELS:
            logger.error(f"Unknown Gemini model: {model_name}. Available: {list(settings.GEMINI_MODELS.keys())}")
            return None

        return model_name

    def get_available_models(self) -> Dict[str, Dict[str, Any]]:
        """Get information about available Gemini models"""
        return settings.GEMINI_MODELS.copy()
    
    def is_file_supported(self, file_data: bytes, filename: str) -> bool:
        """
        Check if file format is supported by Gemini Vision
        
        Args:
            file_data: Raw file bytes
            filename: Original filename
            
        Returns:
            bool: True if format is supported
        """
        # Get MIME type
        mime_type = self._detect_mime_type(file_data, filename)
        return mime_type in self.supported_formats
    
    def _detect_mime_type(self, file_data: bytes, filename: str) -> str:
        """Detect MIME type from file data and filename"""
        # Try to guess from filename first
        mime_type, _ = mimetypes.guess_type(filename)
        
        if mime_type and mime_type in self.supported_formats:
            return mime_type
        
        # Fallback to file signature detection
        if file_data.startswith(b'\x89PNG'):
            return "image/png"
        elif file_data.startswith(b'\xff\xd8\xff'):
            return "image/jpeg"
        elif file_data.startswith(b'RIFF') and b'WEBP' in file_data[:12]:
            return "image/webp"
        elif file_data.startswith(b'GIF8'):
            return "image/gif"
        elif file_data.startswith(b'BM'):
            return "image/bmp"
        elif file_data.startswith(b'II*\x00') or file_data.startswith(b'MM\x00*'):
            return "image/tiff"
        
        # Default fallback
        return "image/jpeg"
    
    def _create_optimized_prompt(self) -> str:
        """
        Create optimized OCR prompt to minimize hallucination
        Based on research findings for best OCR practices
        """
        return """Extract all text from this image accurately and completely. 

IMPORTANT INSTRUCTIONS:
- Extract ONLY the text that is actually visible in the image
- Maintain the original formatting, spacing, and line breaks
- Do not add any interpretations, explanations, or additional content
- Do not guess or infer text that is unclear or partially obscured
- If text is unclear, mark it as [UNCLEAR] rather than guessing
- Include all visible text including headers, footers, watermarks, and small text
- Preserve the reading order (top to bottom, left to right)

Return only the extracted text without any additional commentary."""
    
    async def extract_text(
        self,
        file_data: bytes,
        filename: str,
        model_name: str = None,
        use_preprocessing: bool = True
    ) -> GeminiVisionResult:
        """
        Extract text from image using Gemini Vision

        Args:
            file_data: Raw image bytes
            filename: Original filename
            model_name: Specific Gemini model to use (e.g., "gemini-1.5-pro", "gemini-2.5-flash")
            use_preprocessing: Whether to apply preprocessing (inherited from config)

        Returns:
            GeminiVisionResult with extraction results
        """
        start_time = time.time()

        # Get the model to use
        if model_name is None:
            model_name = settings.GEMINI_MODEL

        model = self.get_model(model_name)
        if not model:
            return GeminiVisionResult(
                success=False,
                text="",
                confidence=0.0,
                processing_time=time.time() - start_time,
                model_used=model_name,
                model_info=settings.GEMINI_MODELS.get(model_name),
                error_message=f"Gemini model not available: {model_name}",
                file_supported=False
            )
        
        # Check if file is supported
        if not self.is_file_supported(file_data, filename):
            logger.info(f"File format not supported by Gemini Vision: {filename}")
            return GeminiVisionResult(
                success=False,
                text="",
                confidence=0.0,
                processing_time=time.time() - start_time,
                model_used=model_name,
                model_info=settings.GEMINI_MODELS.get(model_name),
                error_message="File format not supported by Gemini Vision",
                file_supported=False
            )
        
        try:
            # Detect MIME type
            mime_type = self._detect_mime_type(file_data, filename)
            
            # Create optimized prompt
            prompt = self._create_optimized_prompt()

            # Get model-specific configuration
            model_info = settings.GEMINI_MODELS.get(model_name, {})
            max_tokens = model_info.get("max_tokens", settings.GEMINI_MAX_TOKENS)

            logger.info(f"Processing {filename} with Vertex AI Gemini ({model_name})")

            # Encode image as base64
            image_base64 = base64.b64encode(file_data).decode('utf-8')

            # Create image part for Gemini
            image_part = Part.from_data(
                data=file_data,
                mime_type=mime_type
            )

            # Get the generative model
            model = GenerativeModel(model_name)

            # Generate content with the image and prompt
            response = await asyncio.to_thread(
                model.generate_content,
                [prompt, image_part],
                generation_config={
                    "temperature": settings.GEMINI_TEMPERATURE,
                    "max_output_tokens": max_tokens,
                }
            )

            # Extract text from response
            if response.text:
                extracted_text = response.text.strip()
            else:
                extracted_text = ""
                logger.warning(f"No text extracted from Gemini Vision for {filename}")

            logger.info(f"Gemini Vision extracted {len(extracted_text)} characters from {filename}")
            
            # Calculate confidence (simplified - Gemini doesn't provide confidence scores)
            confidence = self._estimate_confidence(extracted_text)
            
            processing_time = time.time() - start_time

            logger.info(f"Gemini Vision extraction completed: {len(extracted_text)} characters, confidence: {confidence:.3f}")

            return GeminiVisionResult(
                success=True,
                text=extracted_text,
                confidence=confidence,
                processing_time=processing_time,
                model_used=model_name,
                model_info=model_info,
                file_supported=True
            )
            
        except Exception as e:
            logger.error(f"Gemini Vision extraction failed: {str(e)}")
            return GeminiVisionResult(
                success=False,
                text="",
                confidence=0.0,
                processing_time=time.time() - start_time,
                model_used=model_name,
                model_info=settings.GEMINI_MODELS.get(model_name),
                error_message=f"Gemini Vision error: {str(e)}",
                file_supported=True
            )
    
    def _estimate_confidence(self, text: str) -> float:
        """
        Estimate confidence based on text characteristics
        Since Gemini doesn't provide confidence scores, we estimate based on output quality
        """
        if not text or len(text.strip()) == 0:
            return 0.0
        
        # Basic confidence estimation
        confidence = 0.8  # Base confidence for Gemini
        
        # Adjust based on text characteristics
        if len(text) > 50:
            confidence += 0.1  # Longer text usually means better extraction
        
        if '[UNCLEAR]' in text:
            confidence -= 0.2  # Reduce confidence if unclear text is marked
        
        # Check for reasonable text patterns
        if any(char.isalnum() for char in text):
            confidence += 0.05
        
        return min(max(confidence, 0.0), 1.0)

    def cleanup(self):
        """Clean up temporary credentials file"""
        if self.credentials_file:
            try:
                import os
                os.unlink(self.credentials_file)
                logger.info("Cleaned up temporary credentials file")
            except Exception as e:
                logger.warning(f"Failed to clean up credentials file: {e}")
            finally:
                self.credentials_file = None

# Global instance
gemini_vision_engine = GeminiVisionEngine()
