# Balanced Intelligent Document Processor
# Optimized for speed + accuracy across all Zurich document types

DOCUMENT ANALYSIS:
Document Type: {{document_type}}
Geographic Region: {{region}}
Content Quality: {{quality_score}}
Processing Mode: {{processing_mode}}

INTELLIGENT EXTRACTION FOCUS:
{% if document_type == "travel_claim" %}
TRAVEL CLAIM FOCUS:
- Receipt details: vendor, amount, date, currency, purpose
- Claim form data: claimant info, policy number, incident details
- Flight information: numbers, dates, costs, passengers
- Policy coverage: limits, terms, effective dates
{% elif document_type == "motor_claim" %}
MOTOR CLAIM FOCUS:
- Accident details: date, time, location, parties involved
- Vehicle information: registration, make, model, damage assessment
- Liability details: fault determination, coverage analysis
- Claim numbers: policy numbers, incident references
{% elif document_type == "disability_claim" %}
DISABILITY CLAIM FOCUS:
- Medical documentation: diagnosis, treatment, prognosis
- Disability assessment: functional limitations, work capacity
- Benefit details: amounts, duration, conditions
- Claimant information: personal details, employment history
{% elif document_type == "underwriting_application" %}
UNDERWRITING FOCUS:
- Applicant details: business profile, industry, size, location
- Financial data: revenue, assets, liabilities, ratios
- Risk assessment: exposure, claims history, safety measures
- Coverage requested: limits, deductibles, terms
{% elif document_type == "bond_surety" %}
BOND/SURETY FOCUS:
- Bond details: type, amount, duration, beneficiary
- Financial guarantees: collateral, credit rating, capacity
- Applicant profile: financial strength, track record
- Risk factors: project complexity, market conditions
{% elif document_type == "seo_report" %}
SEO MARKETING FOCUS:
- SEO metrics: rankings, traffic, keywords, conversions
- Website performance: page speed, user behavior, technical issues
- Optimization data: content recommendations, structure improvements
- Analytics: demographic data, engagement patterns
{% elif document_type == "financial_report" %}
FINANCIAL FOCUS:
- Financial data: revenue, expenses, profits, ratios
- IFRS17 data: insurance contract liabilities, transitions
- Performance metrics: KPIs, dashboard data, trends
- Reporting details: periods, consolidation, variance analysis
{% elif document_type == "contract" %}
CONTRACT FOCUS:
- Contract terms: duration, value, obligations, penalties
- Party details: contractor, client, guarantors
- Financial terms: pricing, payment schedules, adjustments
- Compliance requirements: regulatory, internal policies
{% else %}
GENERAL DOCUMENT FOCUS:
- Key entities: people, organizations, dates, amounts
- Important numbers: references, IDs, account numbers
- Financial data: currencies, percentages, ratios
- Geographic data: addresses, countries, regions
{% endif %}

QUALITY OPTIMIZATION:
{% if quality_score < 0.7 %}
POOR QUALITY HANDLING:
- Apply OCR error correction using document context
- Use format structure to validate extracted data
- Mark uncertain text with confidence indicators
- Cross-reference with expected document patterns
{% else %}
HIGH QUALITY PROCESSING:
- Preserve exact numerical precision and formatting
- Maintain original terminology and technical language
- Extract metadata and structural relationships
- Validate data consistency across document
{% endif %}

OUTPUT REQUIREMENTS:
Provide response in this exact JSON format:

{
  "document_classification": {
    "primary_category": "CLAIMS|UNDERWRITING|MARKETING|FINANCE|OTHER",
    "document_type": "{{document_type}}",
    "geographic_region": "{{region}}",
    "confidence": 0.95
  },
  "extracted_text": "Complete corrected text with proper formatting",
  "key_entities": {
    "financial_amounts": [
      {"amount": "1000.00", "currency": "USD", "context": "claim_amount"}
    ],
    "dates": [
      {"date": "2024-01-15", "format": "YYYY-MM-DD", "context": "incident_date"}
    ],
    "people": [
      {"name": "John Doe", "role": "claimant", "confidence": 0.95}
    ],
    "organizations": [
      {"name": "ABC Insurance", "role": "insurer", "confidence": 0.98}
    ],
    "reference_numbers": [
      {"number": "POL123456", "type": "policy_number", "confidence": 0.99}
    ],
    "locations": [
      {"address": "123 Main St, Toronto, ON", "type": "incident_location"}
    ]
  },
  "domain_specific_data": {
    {% if document_type in ["travel_claim", "motor_claim", "disability_claim"] %}
    "insurance_claim": {
      "claim_number": "extracted_claim_number",
      "policy_number": "extracted_policy_number",
      "incident_date": "YYYY-MM-DD",
      "claimant_name": "extracted_name",
      "claim_amount": "extracted_amount",
      "status": "extracted_status",
      "coverage_type": "extracted_coverage"
    }
    {% elif document_type in ["underwriting_application", "bond_surety"] %}
    "underwriting": {
      "applicant_name": "extracted_applicant",
      "business_type": "extracted_business_type",
      "coverage_requested": "extracted_coverage",
      "risk_factors": ["factor1", "factor2"],
      "financial_metrics": {"revenue": "amount", "assets": "amount"}
    }
    {% elif document_type == "seo_report" %}
    "marketing": {
      "website_url": "extracted_url",
      "seo_metrics": {"ranking": "position", "traffic": "volume"},
      "performance_data": {"ctr": "percentage", "conversions": "number"},
      "recommendations": ["recommendation1", "recommendation2"]
    }
    {% elif document_type == "financial_report" %}
    "finance": {
      "report_period": "extracted_period",
      "financial_metrics": {"revenue": "amount", "profit": "amount"},
      "key_ratios": {"loss_ratio": "percentage", "expense_ratio": "percentage"},
      "ifrs17_data": {"contract_liabilities": "amount"}
    }
    {% else %}
    "general": {
      "document_purpose": "extracted_purpose",
      "key_information": ["info1", "info2"],
      "action_items": ["action1", "action2"]
    }
    {% endif %}
  },
  "processing_intelligence": {
    "suggested_title": "Intelligent title based on content and type",
    "suggested_tags": ["category", "type", "region", "status", "priority"],
    "suggested_correspondent": "primary_organization_or_person",
    "priority_level": "high|medium|low",
    "next_steps": ["recommended_action_1", "recommended_action_2"]
  },
  "quality_metrics": {
    "overall_confidence": 0.95,
    "extraction_confidence": 0.98,
    "classification_confidence": 0.92,
    "completeness_score": 0.96,
    "processing_notes": ["any_issues_or_recommendations"]
  }
}

CRITICAL INSTRUCTIONS:
- Focus on speed while maintaining accuracy for essential data
- Extract ALL financial amounts with exact precision
- Preserve ALL reference numbers, policy numbers, claim numbers
- Maintain geographic and regional context
- Use document structure to validate extracted information
- Provide confidence scores for uncertain extractions
- Optimize extraction based on document category and region
- Handle multi-currency and multi-language content appropriately
