# Zurich OCR Engine Dependencies

# Core FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
pydantic==2.5.0

# LLM Integration for Intelligent Routing
openai==1.6.1

# Google Document AI Integration
google-cloud-documentai==2.20.1
google-cloud-storage==2.10.0
google-auth==2.25.2
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.1.1

# Gemini Vision via Vertex AI (uses service account)
google-cloud-aiplatform==1.38.1

# OCR and Image Processing
pytesseract==0.3.10
Pillow==10.1.0
opencv-python==********
pdf2image==1.16.3

# Document Processing - Comprehensive Support
PyPDF2==3.0.1
pdfplumber==0.10.3
python-docx==1.1.0
openpyxl==3.1.2
python-pptx==0.6.23
pandas==2.1.4

# Web Content Processing
beautifulsoup4==4.12.2
lxml==4.9.3

# Email Processing
email-validator==2.1.0

# File Format Detection and Processing
python-magic==0.4.27

# Archive Processing
rarfile==4.1
py7zr==0.20.8

# Additional Format Support
xlrd==2.0.1  # Legacy Excel support
PyYAML==6.0.1  # YAML support

# Template Engine for Intelligent Prompts
jinja2==3.1.2

# Utilities and Performance
requests==2.31.0
aiofiles==23.2.1
python-dotenv==1.0.0

# Logging and Monitoring
structlog==23.2.0

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Optional: AWS Integration (for fallback)
boto3==1.34.0
botocore==1.34.0

# Optional: Azure Integration (for fallback)
azure-ai-formrecognizer==3.3.0
azure-core==1.29.5
