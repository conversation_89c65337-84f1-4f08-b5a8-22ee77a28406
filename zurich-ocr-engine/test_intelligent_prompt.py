#!/usr/bin/env python3
"""
Test script for the Intelligent Prompt Processing System
Tests all document types and prompt optimization features
"""

import asyncio
import json
import time
from pathlib import Path

# Test the intelligent prompt processor
async def test_intelligent_prompt_processor():
    """Test the intelligent prompt processor with various document types"""
    
    try:
        from zurich_ocr.core.intelligent_prompt_processor import IntelligentPromptProcessor
        from zurich_ocr.core.document_classifier import DocumentClassifier
        from zurich_ocr.core.template_engine import TemplateEngine
        
        print("🚀 Testing Intelligent Prompt Processing System")
        print("=" * 60)
        
        # Initialize components
        processor = IntelligentPromptProcessor()
        classifier = DocumentClassifier()
        template_engine = TemplateEngine()
        
        print("✅ Components initialized successfully")
        
        # Test document samples for different types
        test_documents = {
            "travel_claim": """
            TRAVEL INSURANCE CLAIM FORM
            Policy Number: POL123456
            Claim Number: CLM789012
            
            Claimant Information:
            Name: <PERSON>
            Date of Birth: 1985-03-15
            
            Trip Details:
            Departure Date: 2024-01-15
            Return Date: 2024-01-22
            Destination: Paris, France
            
            Incident Details:
            Date of Incident: 2024-01-18
            Description: Flight delayed 8 hours due to weather
            Claim Amount: $500.00 CAD
            
            Supporting Documents:
            - Flight booking confirmation
            - Delay certificate from airline
            - Hotel receipts for additional night
            """,
            
            "motor_claim": """
            MOTOR INSURANCE CLAIM REPORT
            Policy Number: POL654321
            Claim Number: CLM345678
            
            Vehicle Information:
            Registration: AB12 CDE
            Make: Toyota
            Model: Camry
            Year: 2020
            
            Accident Details:
            Date: 2024-02-10
            Time: 14:30
            Location: M25 Junction 15, London
            
            Description:
            Rear-end collision at traffic lights. Other driver failed to stop.
            Damage to rear bumper and boot.
            
            Estimated Repair Cost: £2,500
            Third Party: Yes
            Police Report: REF123456
            """,
            
            "financial_report": """
            QUARTERLY FINANCIAL REPORT - Q1 2024
            Zurich Insurance Group
            
            Executive Summary:
            Total Revenue: CHF 15.2 billion
            Net Profit: CHF 1.8 billion
            Loss Ratio: 65.2%
            Expense Ratio: 28.5%
            Combined Ratio: 93.7%
            
            IFRS17 Implementation:
            Contract Service Margin: CHF 12.5 billion
            Risk Adjustment: CHF 3.2 billion
            Contractual Service Margin Release: CHF 850 million
            
            Key Performance Indicators:
            Return on Equity: 14.2%
            Solvency II Ratio: 225%
            Book Value per Share: CHF 385
            
            Regional Performance:
            Europe: CHF 8.5 billion (+5.2%)
            North America: CHF 4.2 billion (+3.8%)
            Asia Pacific: CHF 2.5 billion (+7.1%)
            """,
            
            "seo_report": """
            SEO PERFORMANCE REPORT - MARCH 2024
            Website: zurich.com
            
            Organic Traffic Metrics:
            Total Sessions: 2.5M (+15.2% MoM)
            Organic Sessions: 1.8M (+18.5% MoM)
            Bounce Rate: 32.5% (-2.1% MoM)
            Average Session Duration: 3:45 (+12s MoM)
            
            Keyword Rankings:
            "car insurance": Position 3 (↑2)
            "travel insurance": Position 1 (→)
            "home insurance": Position 5 (↓1)
            "business insurance": Position 2 (↑1)
            
            Technical SEO:
            Page Speed Score: 92/100
            Core Web Vitals: PASS
            Mobile Usability: 98/100
            Crawl Errors: 12 (-8)
            
            Conversion Metrics:
            Quote Requests: 15,250 (+22.3%)
            Conversion Rate: 8.5% (+0.8%)
            Cost per Acquisition: $45.20 (-$3.15)
            """,
            
            "contract": """
            PROCUREMENT CONTRACT AGREEMENT
            Contract Number: CNT2024-001
            
            Parties:
            Contractor: TechSolutions Ltd
            Client: Zurich Insurance Group
            
            Contract Details:
            Service: IT Infrastructure Maintenance
            Contract Value: CHF 2.5 million
            Duration: 3 years (2024-2027)
            Start Date: 2024-04-01
            
            Payment Terms:
            Monthly Payment: CHF 69,444
            Payment Due: 30 days from invoice
            Late Payment Penalty: 2% per month
            
            Key Deliverables:
            - 24/7 system monitoring
            - Monthly security updates
            - Quarterly performance reports
            - Annual disaster recovery testing
            
            Performance Metrics:
            Uptime SLA: 99.9%
            Response Time: <2 hours
            Resolution Time: <24 hours
            """
        }
        
        print(f"\n📋 Testing {len(test_documents)} document types:")
        
        # Test each document type
        for doc_type, content in test_documents.items():
            print(f"\n🔍 Testing {doc_type.replace('_', ' ').title()}")
            print("-" * 40)
            
            start_time = time.time()
            
            # Test classification
            classification = classifier.classify_document(content, f"test_{doc_type}.pdf")
            print(f"📊 Classification: {classification.document_type} ({classification.confidence:.2f})")
            print(f"📂 Category: {classification.category}")
            print(f"🌍 Region: {classification.region}")
            
            # Test template rendering
            try:
                prompt = template_engine.render_prompt(
                    template_name="balanced_intelligent_prompt.tmpl",
                    document_content=content,
                    document_type=classification.document_type,
                    region=classification.region,
                    quality_score=0.9
                )
                print(f"📝 Prompt generated: {len(prompt)} characters")
            except Exception as e:
                print(f"❌ Prompt generation failed: {e}")
            
            # Test full processing (without LLM)
            try:
                result = await processor.process_document(
                    content=content,
                    filename=f"test_{doc_type}.pdf",
                    ocr_confidence=0.9,
                    processing_mode="balanced",
                    llm_client=None  # Test without LLM
                )
                
                processing_time = time.time() - start_time
                
                if result.success:
                    print(f"✅ Processing successful in {processing_time:.2f}s")
                    print(f"📈 Overall confidence: {result.confidence_scores.get('overall', 0):.2f}")
                    print(f"🎯 Extracted entities: {len(result.extracted_data.get('key_entities', {}))}")
                else:
                    print(f"❌ Processing failed: {result.error_message}")
                    
            except Exception as e:
                print(f"❌ Processing error: {e}")
        
        print(f"\n🎉 Intelligent Prompt Processing Test Complete!")
        print("=" * 60)
        
        # Test template availability
        print("\n📁 Available Templates:")
        templates = template_engine.get_available_templates()
        for template in templates:
            print(f"  • {template}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure jinja2 is installed: pip install jinja2==3.1.2")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_endpoint():
    """Test the new intelligent extraction API endpoint"""
    
    print("\n🌐 Testing API Endpoint")
    print("=" * 40)
    
    try:
        import httpx
        
        # Test data
        test_content = """
        TRAVEL INSURANCE CLAIM
        Policy: POL123456
        Claimant: John Smith
        Trip Date: 2024-01-15
        Incident: Flight delay
        Amount: $500 CAD
        """
        
        # Create a simple test file
        test_file = Path("test_document.txt")
        test_file.write_text(test_content)
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                with open(test_file, "rb") as f:
                    files = {"file": ("test_document.txt", f, "text/plain")}
                    data = {"config": json.dumps({"debug_mode": True})}
                    
                    response = await client.post(
                        "http://localhost:8000/api/v1/intelligent-extract",
                        files=files,
                        data=data
                    )
                
                if response.status_code == 200:
                    result = response.json()
                    print("✅ API endpoint working!")
                    print(f"📊 Document type: {result.get('document_type', 'unknown')}")
                    print(f"📈 Confidence: {result.get('confidence', 0):.2f}")
                    print(f"⏱️ Processing time: {result.get('processing_time_ms', 0):.1f}ms")
                else:
                    print(f"❌ API error: {response.status_code}")
                    print(f"Response: {response.text}")
                    
        finally:
            # Cleanup
            if test_file.exists():
                test_file.unlink()
                
    except Exception as e:
        print(f"❌ API test failed: {e}")
        print("Make sure the server is running: uvicorn zurich_ocr.main:app --reload")

if __name__ == "__main__":
    print("🧪 Intelligent Prompt Processing System Test")
    print("=" * 60)
    
    # Run tests
    asyncio.run(test_intelligent_prompt_processor())
    
    # Test API if server is available
    try:
        asyncio.run(test_api_endpoint())
    except:
        print("\n⚠️ API test skipped (server not running)")
        print("To test API: uvicorn zurich_ocr.main:app --reload")
